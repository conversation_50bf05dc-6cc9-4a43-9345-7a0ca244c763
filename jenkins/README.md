# Important Info

## About the automatic docker image generation

A new file has been added to this folder - **docker_tag.txt**. This file contains the version number of the current docker image that is being used. This file must be updated whenever there are changes to the Dockerfile.

When changes are made on the dockerfile inside this folder, the **docker_tag.txt** file must also be updated with the new docker image version so that the automated process knows what version to build and deploy to artifactory.

If this version number is not updated, the CI job that generates the new docker image will fail and prevent the PR associated with the change from being merged.
