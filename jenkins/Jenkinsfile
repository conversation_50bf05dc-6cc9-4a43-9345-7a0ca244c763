// import shared library
@Library('shared-libs') _

String NODE_LABEL_CTRL = 'xtv-ctrl'
String NODE_LABEL_EXECUTOR = 'xtv-exec'

_appUtils = null
_appConstants = null

// Loads libraries in .groovy files
AppUtils = { _appUtils = _appUtils ?: load("${env.WORKSPACE}/jenkins/utils.groovy"); _appUtils }
AppConstants = { _appConstants = _appConstants ?: load("${env.WORKSPACE}/jenkins/constants.groovy"); _appConstants }

pipeline {
  agent { label NODE_LABEL_CTRL }
  stages {
    stage('Tizen-Master-Builds') {
      steps {
        script {
          AppUtils().setupEnvironment()
          parallel AppUtils().TizenBuild(AppConstants().TARGET_TIZEN_MASTER_FLAVOURS, NODE_LABEL_EXECUTOR)
        }
      }
    }
    stage('Tizen-Release-Builds') {
      when {
        branch 'release/*'
      }
      steps {
        script {
          AppUtils().setupEnvironment()
          parallel AppUtils().TizenBuild(AppConstants().TARGET_TIZEN_FLAVOURS, NODE_LABEL_EXECUTOR)
        }
      }
    }
  }
  post {
    success {
      script {
        currentBuild.result = 'SUCCESS'
      }
    }
    unsuccessful {
      script {
        currentBuild.result = 'FAILURE'
      }
    }
    cleanup {
      cleanWs()
    }
  }
}
