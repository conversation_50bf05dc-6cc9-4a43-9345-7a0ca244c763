#!/bin/bash
set +x
mkdir /home/<USER>/tizen-studio-data/profile
cp profile/profiles.xml /home/<USER>/tizen-studio-data/profile

sed -i 's/\"author\"/\"\/home\/<USER>\/author.p12\"/g' /home/<USER>/tizen-studio-data/profile/profiles.xml
sed -i 's/\"distributor\"/\"\/home\/<USER>\/distributor.p12\"/g' /home/<USER>/tizen-studio-data/profile/profiles.xml

AUTHOR_PASS_ESC=$(printf '%s\n' "$AUTHOR_PASS" | sed -e 's/[\/&]/\\&/g')
DISTRIBUTOR_PASS_ESC=$(printf '%s\n' "$DISTRIBUTOR_PASS" | sed -e 's/[\/&]/\\&/g')

sed -i s/a-password/${AUTHOR_PASS_ESC}/g /home/<USER>/tizen-studio-data/profile/profiles.xml
sed -i s/d-password/${DISTRIBUTOR_PASS_ESC}/g /home/<USER>/tizen-studio-data/profile/profiles.xml

set -x

yarn

overideFlag=""

if [[ -n $CUSTOMURL ]]
then
  overideFlag="-c ${CUSTOMURL}"
fi

PACKAGE_NAME="tizen-${BUILDIDENTIFIER}-${COMMIT_HASH}"

yarn build --territory ${TERRITORY} --proposition ${PROPOSITION} --environment ${ENVIRONMENT} $overideFlag --device oobe-voice
tizen security-profiles list
tizen package -s peacock -t wgt -- dist
cat /home/<USER>/tizen-studio-data/profile/profiles.xml
mv dist/*.wgt ${PACKAGE_NAME}-oobe-voice.wgt

yarn build --territory ${TERRITORY} --proposition ${PROPOSITION} --environment ${ENVIRONMENT} $overideFlag --device default
tizen security-profiles list
tizen package -s peacock -t wgt -- dist
cat /home/<USER>/tizen-studio-data/profile/profiles.xml
mv dist/*.wgt ${PACKAGE_NAME}-default.wgt

tar -czf ${PACKAGE_NAME}.tar.gz *.wgt

exit 0
