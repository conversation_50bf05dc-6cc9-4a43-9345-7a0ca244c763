/* groovylint-disable NestedBlockDepth, UnnecessaryGetter */
// Utilities for android-cli-lib jenkins

//update status in github
void githubStatus(String name, String status) {
  String url = env.RUN_DISPLAY_URL
  withCredentials([usernameColonPassword(credentialsId: 'jenkins-nbcu-access-bot', variable: 'GH_ACCESS_TOKEN')]) {
    sh """
      curl \
        -X POST \
        -H "Accept: application/vnd.github.v3+json" \
        "https://api.github.com/repos/nbcudtc/client-container-tizen/statuses/\$GIT_COMMIT" \
        -d '{"context": "continuous-integration/${name}", "description": "${name}", "state": "${status}", "target_url": "${url}"}' \
        -u \$GH_ACCESS_TOKEN
    """
  }
}

boolean isPullRequest() {
  return env.CHANGE_ID || env.<PERSON><PERSON><PERSON>_NAME.startsWith('PR')
}

String getFileChanged(String filePath) {
  if (isPullRequest()) {
    //for PRs compare with main so that all files changed in PR are listed
    return sh(script:
                "git --no-pager diff --name-only origin/main...${env.GIT_COMMIT} | grep -i ${filePath} | sort -u ",
                returnStdout: true)
  }
  return sh(script:
            "git --no-pager diff --name-only ${env.GIT_COMMIT}^! | grep -i ${filePath} | sort -u ",
            returnStdout: true)
}

boolean isReleaseBranch() {
  return env.BRANCH_NAME && env.BRANCH_NAME.startsWith('release/')
}

//Sets up the required env vars
def setupEnvironment() {
  env.BASE_COMMIT = sh(script: 'git log HEAD -1 --format=format:%H', returnStdout: true).trim()
  env.GIT_HASH = env.BASE_COMMIT.substring(0, 7)
  env.VERSION = env.GIT_HASH
  if (isReleaseBranch()) {
    try {
      sh(script: "git describe --tags --exact-match ${env.GIT_HASH}")
        } catch (err) {
      error('ERROR: Cannot run release build with an untagged commit - commit has no tags')
    }
  }
}

def TizenWithParamsBuild(Object vaultSecrets) {
  withVault([configuration: AppConstants().xtvVaultConfigs, vaultSecrets: vaultSecrets]) {
    // write certificate files to the expected paths
    String PATH_TO_UPLOAD_DISTRIBUTOR = "${env.WORKSPACE}/distributor.p12"
    String PATH_TO_AUTHOR = "${env.WORKSPACE}/author.p12"

    writeFile(file: PATH_TO_UPLOAD_DISTRIBUTOR, text: env.DISTRIBUTOR_CONTENT_BASE64, encoding: 'Base64')
    writeFile(file: PATH_TO_AUTHOR, text: env.AUTHOR_CONTENT_BASE64, encoding: 'Base64')

    sh """
      set +x
      cp ${PATH_TO_UPLOAD_DISTRIBUTOR} /home/<USER>/distributor.p12
      cp ${PATH_TO_AUTHOR} /home/<USER>/author.p12
      ./jenkins/release-task.sh
    """
  }
}

def slack(String message, String colour) {
  slackSend channel: '#peacock-xtv-container-builds', color: colour, teamDomain: 'sky', tokenCredentialId: 'slack-ci-token', message: """
    ${message}
  """
}

def uploadToArtifactory(String path, String artifact) {
  String target = "${AppConstants().ARTIFACTORY_GENERIC_REPO}/${path}"
  // set env variable, with URL to artifactory bundle location
  env.ARTIFACTORY_BUNDLE_URL = "${AppConstants().ARTIFACTORY_BASE_URL}/${target}/${artifact}"
  //use shared lib to upload to new NBCU artifactory
  artifactoryLib.upload(artifact, target)
}

Map TizenBuild(List buildPlatforms, String NODE_LABEL) {
  Map deviceBuilds = [:]
  buildPlatforms.each { buildPlatform ->
    deviceBuilds["${STAGE_NAME}-${buildPlatform.BUILDIDENTIFIER}-${env.GIT_HASH}"] = {
      node(NODE_LABEL) {
        checkout scm

        try {
          docker.withRegistry(AppConstants().DOCKER_REGISTRY, AppConstants().DOCKER_REGISTRY_CREDS) {
            docker.image("${AppConstants().DOCKER_IMAGE_NAME}:${dockerImgTag()}").inside('-u 0:0') {
              withEnv([
                "BUILDIDENTIFIER=${buildPlatform.BUILDIDENTIFIER}",
                "ENVIRONMENT=${buildPlatform.ENVIRONMENT}",
                "TERRITORY=${buildPlatform.TERRITORY}",
                "PACKAGE=${buildPlatform.PACKAGE}",
                "PROPOSITION=${buildPlatform.PROPOSITION}",
                "COMMIT_HASH=${env.GIT_HASH}",
                "CUSTOMURL=${buildPlatform.CUSTOMURL}",
              ]) {
                AppUtils().TizenWithParamsBuild(buildPlatform.VAULT_SECRETS)
                def DIST_FUNCTIONAL_BUNDLE = "tizen-${BUILDIDENTIFIER}-${COMMIT_HASH}.tar.gz"

                AppUtils().uploadToArtifactory(AppConstants().ARTIFACTORY_CONTAINERS_TIZEN, DIST_FUNCTIONAL_BUNDLE)

                // Write artifactory url to file, to be read from XTV/LIGHTNING/PLANNED-RELEASE-NOTES.
                // File format name: "PROPOSITION-TERRITORY-ENVIRONMENT-artifact-url.txt"
                String resultFile = "${buildPlatform.PROPOSITION}-${buildPlatform.TERRITORY}-${buildPlatform.ENVIRONMENT}-artifact-url.txt"
                sh "echo '${env.ARTIFACTORY_BUNDLE_URL}' > ${resultFile}"
                archiveArtifacts(allowEmptyArchive: false, artifacts: resultFile)

                AppUtils().slack(":samsung: Tizen build created - ${DIST_FUNCTIONAL_BUNDLE} ${AppConstants().ARTIFACTORY_BASE_URL}/${AppConstants().ARTIFACTORY_GENERIC_REPO}/${AppConstants().ARTIFACTORY_CONTAINERS_TIZEN}/${DIST_FUNCTIONAL_BUNDLE}", '#4286c5')
              }
            }
          }
        }
        finally {
          cleanWs()
        }
      }
    }
  }
  return deviceBuilds
}

String dockerImgTag() {
  return sh(script: "cat ${AppConstants().DOCKER_IMAGE_FOLDER}/${AppConstants().DOCKER_TAG_FILE}", returnStdout: true).trim()
}

return this
