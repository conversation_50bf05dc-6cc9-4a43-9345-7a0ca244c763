FROM node:22.10.0-slim

RUN apt update && \
    apt install -y pciutils zip libxml2-utils xmlstarlet curl

RUN useradd -ms /bin/bash newuser
USER newuser
WORKDIR /home/<USER>
RUN curl -L "https://download.tizen.org/sdk/Installer/tizen-studio_4.6/web-cli_Tizen_Studio_4.6_ubuntu-64.bin" > web-cli_Tizen_Studio_4.6_ubuntu-64.bin \
    && chmod +x web-cli_Tizen_Studio_4.6_ubuntu-64.bin \
    && chown newuser web-cli_Tizen_Studio_4.6_ubuntu-64.bin
USER newuser
RUN  ./web-cli_Tizen_Studio_4.6_ubuntu-64.bin --accept-license --no-java-check /home/<USER>/tizen-studio
USER root
ENV PATH="/home/<USER>/tizen-studio/tools/ide/bin:${PATH}"
RUN rm web-cli_Tizen_Studio_4.6_ubuntu-64.bin
