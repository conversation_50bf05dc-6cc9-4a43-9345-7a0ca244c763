//constants
import groovy.transform.Field

@Field
String ARTIFACTORY_GENERIC_REPO = 'gstapps-generic-local-prod'

@Field
String ARTIFACTORY_CONTAINERS_TIZEN = 'containers/tizen'

@Field
String ARTIFACTORY_BASE_URL = 'https://artifactory.nbcucloud.nbcuott.com/artifactory'

@Field
String DOCKER_REGISTRY = 'https://104553785803.dkr.ecr.us-west-2.amazonaws.com'

@Field
String DOCKER_REGISTRY_CREDS = 'ecr:us-west-2:terraform-jkci'

@Field
String DOCKER_IMAGE_NAME = '104553785803.dkr.ecr.us-west-2.amazonaws.com/xtv/containers/tv-devices-tizen'

@Field
String DOCKER_IMAGE_FOLDER = 'jenkins' //dckrImgFolder

@Field
String DOCKER_TAG_FILE = 'docker_tag.txt' //dckrTagFile

// Hashicorp Vault
@Field
Map xtvVaultConfigs = [
  vaultUrl: 'https://vault.cliappsre.mgmt-awslocal.nbcuott.com:8200/',
  vaultCredentialId: 'jk-xtv-ar',
  engineVersion: 2
]

@Field
Object nowottSecrets = [
  [path: 'secret/peacock-cliapps-xtv/client-container-tizen/nowott', engineVersion: 2, secretValues: [
    [envVar: 'DISTRIBUTOR_CONTENT_BASE64', vaultKey: 'distributor_b64'],
    [envVar: 'DISTRIBUTOR_PASS', vaultKey: 'dist_password'],
    [envVar: 'AUTHOR_CONTENT_BASE64', vaultKey: 'author_b64'],
    [envVar: 'AUTHOR_PASS', vaultKey: 'auth_password']
  ]]
]

@Field
Object nowottDESecrets = [
  [path: 'secret/peacock-cliapps-xtv/client-container-tizen/nowottDE', engineVersion: 2, secretValues: [
    [envVar: 'DISTRIBUTOR_CONTENT_BASE64', vaultKey: 'distributor_b64'],
    [envVar: 'DISTRIBUTOR_PASS', vaultKey: 'dist_password'],
    [envVar: 'AUTHOR_CONTENT_BASE64', vaultKey: 'author_b64'],
    [envVar: 'AUTHOR_PASS', vaultKey: 'auth_password']
  ]]
]

@Field
Object nowottIESecrets = [
  [path: 'secret/peacock-cliapps-xtv/client-container-tizen/nowottIE', engineVersion: 2, secretValues: [
    [envVar: 'DISTRIBUTOR_CONTENT_BASE64', vaultKey: 'distributor_b64'],
    [envVar: 'DISTRIBUTOR_PASS', vaultKey: 'dist_password'],
    [envVar: 'AUTHOR_CONTENT_BASE64', vaultKey: 'author_b64'],
    [envVar: 'AUTHOR_PASS', vaultKey: 'auth_password']
  ]]
]

@Field
Object nowottITSecrets = [
  [path: 'secret/peacock-cliapps-xtv/client-container-tizen/nowottIT', engineVersion: 2, secretValues: [
    [envVar: 'DISTRIBUTOR_CONTENT_BASE64', vaultKey: 'distributor_b64'],
    [envVar: 'DISTRIBUTOR_PASS', vaultKey: 'dist_password'],
    [envVar: 'AUTHOR_CONTENT_BASE64', vaultKey: 'author_b64'],
    [envVar: 'AUTHOR_PASS', vaultKey: 'auth_password']
  ]]
]

@Field
Object nowottUKSecrets = [
  [path: 'secret/peacock-cliapps-xtv/client-container-tizen/nowottUK', engineVersion: 2, secretValues: [
    [envVar: 'DISTRIBUTOR_CONTENT_BASE64', vaultKey: 'distributor_b64'],
    [envVar: 'DISTRIBUTOR_PASS', vaultKey: 'dist_password'],
    [envVar: 'AUTHOR_CONTENT_BASE64', vaultKey: 'author_b64'],
    [envVar: 'AUTHOR_PASS', vaultKey: 'auth_password']
  ]]
]

@Field
Object peacockSecrets = [
  [path: 'secret/peacock-cliapps-xtv/client-container-tizen/peacock', engineVersion: 2, secretValues: [
    [envVar: 'DISTRIBUTOR_CONTENT_BASE64', vaultKey: 'distributor_b64'],
    [envVar: 'DISTRIBUTOR_PASS', vaultKey: 'dist_password'],
    [envVar: 'AUTHOR_CONTENT_BASE64', vaultKey: 'author_b64'],
    [envVar: 'AUTHOR_PASS', vaultKey: 'auth_password']
  ]]
]

@Field
Object showmaxSecrets = [
  [path: 'secret/peacock-cliapps-xtv/client-container-tizen/showmax', engineVersion: 2, secretValues: [
    [envVar: 'DISTRIBUTOR_CONTENT_BASE64', vaultKey: 'distributor_b64'],
    [envVar: 'DISTRIBUTOR_PASS', vaultKey: 'dist_password'],
    [envVar: 'AUTHOR_CONTENT_BASE64', vaultKey: 'author_b64'],
    [envVar: 'AUTHOR_PASS', vaultKey: 'auth_password']
  ]]
]

@Field
Object skyshowtimeSecrets = [
  [path: 'secret/peacock-cliapps-xtv/client-container-tizen/skyshowtime', engineVersion: 2, secretValues: [
    [envVar: 'DISTRIBUTOR_CONTENT_BASE64', vaultKey: 'distributor_b64'],
    [envVar: 'DISTRIBUTOR_PASS', vaultKey: 'dist_password'],
    [envVar: 'AUTHOR_CONTENT_BASE64', vaultKey: 'author_b64'],
    [envVar: 'AUTHOR_PASS', vaultKey: 'auth_password']
  ]]
]

class BuildConfig {

  String BUILDIDENTIFIER
  String ENVIRONMENT
  String TERRITORY
  String PROPOSITION
  String PACKAGE
  Object VAULT_SECRETS
  String CUSTOMURL

  BuildConfig(String BUILDIDENTIFIER, String PACKAGE, String PROPOSITION, String ENVIRONMENT, String TERRITORY, Object VAULT_SECRETS, String CUSTOMURL = '') {
    this.BUILDIDENTIFIER = BUILDIDENTIFIER
    this.ENVIRONMENT = ENVIRONMENT
    this.TERRITORY = TERRITORY
    this.PROPOSITION = PROPOSITION
    this.PACKAGE = PACKAGE
    this.VAULT_SECRETS = VAULT_SECRETS
    this.CUSTOMURL = CUSTOMURL
  }

}

//this is dumb but I couldnt get the class on the Jenkinsfile any other way
BuildConfig makeBuildConfig(String BUILDIDENTIFIER, String PACKAGE, String PROPOSITION, String ENVIRONMENT, String TERRITORY, Object VAULT_SECRETS, String CUSTOMURL = '') {
  return new BuildConfig(BUILDIDENTIFIER, PACKAGE, PROPOSITION, ENVIRONMENT, TERRITORY, VAULT_SECRETS, CUSTOMURL)
}

@Field
BuildConfig peacockStableMaster = new BuildConfig('peacock-stable-master', 'peacock', 'peacock', 'stable', 'US', peacockSecrets)

@Field
BuildConfig peacockMaster = new BuildConfig('peacock-master', 'peacock', 'peacock', 'production', 'US', peacockSecrets)

@Field
BuildConfig peacockReleaseProduction = new BuildConfig('peacock-release-production', 'peacock', 'peacock', 'production', 'US', peacockSecrets)

@Field
BuildConfig peacockReleaseProton = new BuildConfig('peacock-release-proton', 'peacock', 'peacock', 'proton', 'US',  peacockSecrets)

@Field
BuildConfig peacockReleasePreview = new BuildConfig('peacock-release-preview', 'peacock', 'peacock', 'preview', 'US',  peacockSecrets)

@Field
BuildConfig peacockReleaseRC = new BuildConfig('peacock-release-rc', 'peacock', 'peacock', 'rc', 'US',  peacockSecrets)

@Field
BuildConfig peacockReleaseStablePreview = new BuildConfig('peacock-release-stable-preview', 'peacock', 'peacock', 'stable-preview', 'US',  peacockSecrets)

@Field
BuildConfig skyshowtimeStableMaster = new BuildConfig('skyshowtime-stable-master', 'skyshowtime', 'skyshowtime', 'stable', 'SST', skyshowtimeSecrets)

@Field
BuildConfig skyshowtimeMaster = new BuildConfig('skyshowtime-master', 'skyshowtime', 'skyshowtime', 'production', 'SST', skyshowtimeSecrets)

@Field
BuildConfig skyshowtimeReleaseProduction = new BuildConfig('skyshowtime-release-production', 'skyshowtime', 'skyshowtime', 'production', 'SST', skyshowtimeSecrets)

@Field
BuildConfig skyshowtimeReleaseProton = new BuildConfig('skyshowtime-release-proton', 'skyshowtime', 'skyshowtime', 'proton', 'SST', skyshowtimeSecrets)

@Field
BuildConfig skyshowtimeReleasePreview = new BuildConfig('skyshowtime-release-preview', 'skyshowtime', 'skyshowtime', 'preview', 'SST', skyshowtimeSecrets)

@Field
BuildConfig skyshowtimeReleaseRC = new BuildConfig('skyshowtime-release-rc', 'skyshowtime', 'skyshowtime', 'rc', 'SST', skyshowtimeSecrets)

@Field
BuildConfig skyshowtimeReleaseStablePreview = new BuildConfig('skyshowtime-release-stable-preview', 'skyshowtime', 'skyshowtime', 'stable-preview', 'SST', skyshowtimeSecrets)

@Field
BuildConfig showmaxStableMaster = new BuildConfig('showmax-stable-master', 'showmax', 'showmax', 'stable', 'SM', showmaxSecrets)

@Field
BuildConfig showmaxMaster = new BuildConfig('showmax-master', 'showmax', 'showmax', 'production', 'SM', showmaxSecrets)

@Field
BuildConfig showmaxReleaseProduction = new BuildConfig('showmax-release-production', 'showmax', 'showmax', 'production', 'SM', showmaxSecrets)

@Field
BuildConfig showmaxReleaseProton = new BuildConfig('showmax-release-proton', 'showmax', 'showmax', 'proton', 'SM', showmaxSecrets)

@Field
BuildConfig showmaxReleasePreview = new BuildConfig('showmax-release-preview', 'showmax', 'showmax', 'preview', 'SM', showmaxSecrets)

@Field
BuildConfig showmaxReleaseRC = new BuildConfig('showmax-release-rc', 'showmax', 'showmax', 'rc', 'SM', showmaxSecrets)

@Field
BuildConfig showmaxReleaseStablePreview = new BuildConfig('showmax-release-stable-preview', 'showmax', 'showmax', 'stable-preview', 'SM', showmaxSecrets)

@Field
BuildConfig nowtvGBStableMaster = new BuildConfig('nowtvGB-stable-master', 'nowtvGB', 'nowott', 'stable', 'GB', nowottUKSecrets)

@Field
BuildConfig nowtvGBMaster = new BuildConfig('nowtvGB-master', 'nowtvGB', 'nowott', 'production', 'GB', nowottUKSecrets)

@Field
BuildConfig nowtvGBReleaseProduction = new BuildConfig('nowtvGB-release-production', 'nowtvGB', 'nowott', 'production', 'GB', nowottUKSecrets)

@Field
BuildConfig nowtvGBReleaseProton = new BuildConfig('nowtvGB-release-proton', 'nowtvGB', 'nowott', 'proton', 'GB', nowottUKSecrets)

@Field
BuildConfig nowtvGBReleasePreview = new BuildConfig('nowtvGB-release-preview', 'nowtvGB', 'nowott', 'preview', 'GB', nowottUKSecrets)

@Field
BuildConfig nowtvGBReleaseRC = new BuildConfig('nowtvGB-release-rc', 'nowtvGB', 'nowott', 'rc', 'GB', nowottUKSecrets)

@Field
BuildConfig nowtvGBReleaseStablePreview = new BuildConfig('nowtvGB-release-stable-preview', 'nowtvGB', 'nowott', 'stable-preview', 'GB', nowottUKSecrets)

@Field
BuildConfig nowtvITStableMaster = new BuildConfig('nowtvIT-stable-master', 'nowtvIT', 'nowott', 'stable', 'IT', nowottITSecrets)

@Field
BuildConfig nowtvITMaster = new BuildConfig('nowtvIT-master', 'nowtvIT', 'nowott', 'production', 'IT', nowottITSecrets)

@Field
BuildConfig nowtvITReleaseProduction = new BuildConfig('nowtvIT-release-production', 'nowtvIT', 'nowott', 'production', 'IT', nowottITSecrets)

@Field
BuildConfig nowtvITReleaseProton = new BuildConfig('nowtvIT-release-proton', 'nowtvIT', 'nowott', 'proton', 'IT', nowottITSecrets)

@Field
BuildConfig nowtvITReleasePreview = new BuildConfig('nowtvIT-release-preview', 'nowtvIT', 'nowott', 'preview', 'IT', nowottITSecrets)

@Field
BuildConfig nowtvITReleaseRC = new BuildConfig('nowtvIT-release-rc', 'nowtvIT', 'nowott', 'rc', 'IT', nowottITSecrets)

@Field
BuildConfig nowtvITReleaseStablePreview = new BuildConfig('nowtvIT-release-stable-preview', 'nowtvIT', 'nowott', 'stable-preview', 'IT', nowottITSecrets)

@Field
BuildConfig nowtvIEStableMaster = new BuildConfig('nowtvIE-stable-master', 'nowtvIE', 'nowott', 'stable', 'IE', nowottIESecrets)

@Field
BuildConfig nowtvIEMaster = new BuildConfig('nowtvIE-master', 'nowtvIE', 'nowott', 'production', 'IE', nowottIESecrets)

@Field
BuildConfig nowtvIEReleaseProduction = new BuildConfig('nowtvIE-release-production', 'nowtvIE', 'nowott', 'production', 'IE', nowottIESecrets)

@Field
BuildConfig nowtvIEReleaseProton = new BuildConfig('nowtvIE-release-proton', 'nowtvIE', 'nowott', 'proton', 'IE', nowottIESecrets)

@Field
BuildConfig nowtvIEReleasePreview = new BuildConfig('nowtvIE-release-preview', 'nowtvIE', 'nowott', 'preview', 'IE', nowottIESecrets)

@Field
BuildConfig nowtvIEReleaseRC = new BuildConfig('nowtvIE-release-rc', 'nowtvIE', 'nowott', 'rc', 'IE', nowottIESecrets)

@Field
BuildConfig nowtvIEReleaseStablePreview = new BuildConfig('nowtvIE-release-stable-preview', 'nowtvIE', 'nowott', 'stable-preview', 'IE', nowottIESecrets)

@Field
BuildConfig nowtvDEStableMaster = new BuildConfig('nowtvDE-stable-master', 'nowtvDE', 'nowott', 'stable', 'DE', nowottDESecrets)

@Field
BuildConfig nowtvDEMaster = new BuildConfig('nowtvDE-master', 'nowtvDE', 'nowott', 'production', 'DE', nowottDESecrets)

@Field
BuildConfig nowtvDEReleaseProduction = new BuildConfig('nowtvDE-release-production', 'nowtvDE', 'nowott', 'production', 'DE', nowottDESecrets)

@Field
BuildConfig nowtvDEReleaseProton = new BuildConfig('nowtvDE-release-proton', 'nowtvDE', 'nowott', 'proton', 'DE', nowottDESecrets)

@Field
BuildConfig nowtvDEReleasePreview = new BuildConfig('nowtvDE-release-preview', 'nowtvDE', 'nowott', 'preview', 'DE', nowottDESecrets)

@Field
BuildConfig nowtvDEReleaseRC = new BuildConfig('nowtvDE-release-rc', 'nowtvDE', 'nowott', 'rc', 'DE', nowottDESecrets)

@Field
BuildConfig nowtvDEReleaseStablePreview = new BuildConfig('nowtvDE-release-stable-preview', 'nowtvDE', 'nowott', 'stable-preview', 'DE', nowottDESecrets)

@Field
ArrayList TARGET_TIZEN_FLAVOURS = [
    peacockReleaseProduction,
    peacockReleaseProton,
    peacockReleasePreview,
    peacockReleaseRC,
    peacockReleaseStablePreview,
    skyshowtimeReleaseProduction,
    skyshowtimeReleaseProton,
    skyshowtimeReleasePreview,
    skyshowtimeReleaseRC,
    skyshowtimeReleaseStablePreview,
    showmaxReleaseProduction,
    showmaxReleaseProton,
    showmaxReleasePreview,
    showmaxReleaseRC,
    showmaxReleaseStablePreview,
    nowtvGBReleaseProduction,
    nowtvGBReleaseProton,
    nowtvGBReleasePreview,
    nowtvGBReleaseRC,
    nowtvGBReleaseStablePreview,
    nowtvIEReleaseProduction,
    nowtvIEReleaseProton,
    nowtvIEReleasePreview,
    nowtvIEReleaseRC,
    nowtvIEReleaseStablePreview,
    nowtvITReleaseProduction,
    nowtvITReleaseProton,
    nowtvITReleasePreview,
    nowtvITReleaseRC,
    nowtvITReleaseStablePreview,
    nowtvDEReleaseProduction,
    nowtvDEReleaseProton,
    nowtvDEReleasePreview,
    nowtvDEReleaseRC,
    nowtvDEReleaseStablePreview
]

@Field
ArrayList TARGET_TIZEN_MASTER_FLAVOURS = [
    peacockStableMaster,
    peacockMaster,
    skyshowtimeStableMaster,
    skyshowtimeMaster,
    showmaxStableMaster,
    showmaxMaster,
    nowtvGBMaster,
    nowtvIEMaster,
    nowtvITMaster,
    nowtvDEMaster
]

return this
