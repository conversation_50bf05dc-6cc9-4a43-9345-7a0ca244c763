// import shared library
@Library('shared-libs') _
import groovy.transform.Field

String NODE_LABEL_CTRL = 'xtv-ctrl'
String NODE_LABEL_EXECUTOR = 'xtv-exec'

_appConstants = null
_appUtils = null

// Loads libraries in .groovy files
AppUtils = { _appUtils = _appUtils ?: load("${env.WORKSPACE}/jenkins/utils.groovy"); _appUtils }
AppConstants = { _appConstants = _appConstants ?: load("${env.WORKSPACE}/jenkins/constants.groovy"); _appConstants }

pipeline {
  agent {
      label NODE_LABEL_CTRL
  }
  stages {
    stage('Manual-Job') {
      steps {
        script {
          String PACKAGE = params.PROPOSITION
          String TERRITORY = 'US'
          String CUSTOM_PROPOSITION = 'peacocktv'
          // String DISTRIBUTOR = 'tizen-container-dist-cert-p12'
          // String AUTHOR = 'tizen-container-author-cert-p12'
          // String DISTRIBUTOR_PASSWORD = 'tizen-container-dist-cert-pwd'
          // String AUTHOR_PASSWORD = 'tizen-container-author-cert-pwd'
          Object VAULT_SECRETS = AppConstants().peacockSecrets
          String INDEX_CONFIG_TYPE = params.INDEX_CONFIG_TYPE ?: 'index.html'

          if (params.PROPOSITION == 'skyshowtime') {
            TERRITORY = 'EU'
            CUSTOM_PROPOSITION = 'skyshowtime'
            VAULT_SECRETS = AppConstants().skyshowtimeSecrets
            // DISTRIBUTOR = params.PROPOSITION + '-' + DISTRIBUTOR
            // AUTHOR = params.PROPOSITION + '-' + AUTHOR
            // DISTRIBUTOR_PASSWORD = params.PROPOSITION + '-' + DISTRIBUTOR_PASSWORD
            // AUTHOR_PASSWORD = params.PROPOSITION + '-' + AUTHOR_PASSWORD
          }

          if (params.PROPOSITION == 'showmax') {
            TERRITORY = 'AU'
            CUSTOM_PROPOSITION = 'ott.showmax'
            VAULT_SECRETS = AppConstants().showmaxSecrets
            // DISTRIBUTOR = params.PROPOSITION + '-' + DISTRIBUTOR
            // AUTHOR = params.PROPOSITION + '-' + AUTHOR
            // DISTRIBUTOR_PASSWORD = params.PROPOSITION + '-' + DISTRIBUTOR_PASSWORD
            // AUTHOR_PASSWORD = params.PROPOSITION + '-' + AUTHOR_PASSWORD
          }

          if (params.PROPOSITION == 'nowott') {
            TERRITORY = params.TERRITORY
            CUSTOM_PROPOSITION = 'nowtv'
            // update accordingly once the certs will be ready
            VAULT_SECRETS = AppConstants().nowottSecrets

            // DISTRIBUTOR = 'tizen-container-dist-cert-p12'
            // AUTHOR = 'tizen-container-author-cert-p12'
            // DISTRIBUTOR_PASSWORD = 'tizen-container-dist-cert-pwd'
            // AUTHOR_PASSWORD = 'tizen-container-author-cert-pwd'

            // UK is used for both GB & IE
            String INDEX_TERRITORY = 'nowuk'
            if (TERRITORY == 'DE') {
              INDEX_TERRITORY = 'nowdeutschland'
            }
            if (TERRITORY == 'IT') {
              INDEX_TERRITORY = 'nowitalia'
            }
            INDEX_CONFIG_TYPE = INDEX_CONFIG_TYPE.replaceFirst('index', "index-${INDEX_TERRITORY}")
          }

          AppUtils().setupEnvironment()

          BUILD_ENVS.split(',').each { ENVIRONMENT ->
            String URL = ''
            String CUSTOM_TERRITORY = TERRITORY
            String IDENTIFIER_OVERRIDE = ''
            String ENV_PROPOSITION = CUSTOM_PROPOSITION
            List BUILD_CONFIGS = []

            if (params.RELEASEOVERRIDE) {
              if (ENVIRONMENT == 'production' || ENVIRONMENT == 'rc') {
                IDENTIFIER_OVERRIDE = "-${params.RELEASEOVERRIDE}"
                URL = "https://tv.clients.${ENV_PROPOSITION}.com/lightning/release/prod/${params.PLATFORM}/${params.RELEASEOVERRIDE}/${INDEX_CONFIG_TYPE}"
              }

              if (ENVIRONMENT == 'stable') {
                IDENTIFIER_OVERRIDE = "-${params.RELEASEOVERRIDE}"
                ENV_PROPOSITION = params.PROPOSITION == 'showmax' ? 'gottmax' : ENV_PROPOSITION
                URL = "https://tv.clients.stable-int.${ENV_PROPOSITION}.com/lightning/release/stable-int/${params.PLATFORM}/${params.RELEASEOVERRIDE}/${INDEX_CONFIG_TYPE}"
              }

              if (ENVIRONMENT == 'proton') {
                IDENTIFIER_OVERRIDE = "-${params.RELEASEOVERRIDE}"
                URL = "https://tv-proton.clients.${ENV_PROPOSITION}.com/lightning/release/proton/${params.PLATFORM}/${params.RELEASEOVERRIDE}/${INDEX_CONFIG_TYPE}"
              }
            }

            if (params.URLOVERRIDE) {
              IDENTIFIER_OVERRIDE = '-customurl'
              URL = params.URLOVERRIDE
            }

            IDENTIFIER_OVERRIDE += params.PLATFORM == 'tizen' ? '-default' : '-2016'

            String BUILD_IDENTIFIER = "${BUILD_NUMBER}-${params.PROPOSITION}-${CUSTOM_TERRITORY}-${ENVIRONMENT}${IDENTIFIER_OVERRIDE}"

            BUILD_CONFIGS.add(AppConstants().makeBuildConfig(BUILD_IDENTIFIER, PACKAGE, params.PROPOSITION, ENVIRONMENT, CUSTOM_TERRITORY, VAULT_SECRETS, URL))

            parallel AppUtils().TizenBuild(BUILD_CONFIGS, NODE_LABEL_EXECUTOR)
          }
        }
      }
    }
  }
  post {
    success {
      script {
        currentBuild.result = 'SUCCESS'
        AppUtils().githubStatus('prepare', 'success')
      }
    }
    unsuccessful {
      script {
        currentBuild.result = 'FAILURE'
        AppUtils().githubStatus('prepare', 'failure')
      }
    }
    cleanup {
      cleanWs()
    }
  }
}
