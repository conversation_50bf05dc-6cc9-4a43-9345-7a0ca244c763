/* groovylint-disable CompileStat<PERSON>, NestedBlockDepth */
// import shared library
@Library('shared-libs') _

String NODE_LABEL = 'xtv-exec'
String versionFileChanges = ''
String dockerFileChanges = ''
def containersImg = ''
String imgTag = 'latest'

_appUtils = null
_appConstants = null

AppUtils = { _appUtils = _appUtils ?: load("${env.WORKSPACE}/jenkins/utils.groovy"); _appUtils }
AppConstants = { _appConstants = _appConstants ?: load("${env.WORKSPACE}/jenkins/constants.groovy"); _appConstants }

pipeline {
  agent { label NODE_LABEL }
  stages {
    stage('prepare') {
      steps {
        script {
          versionFileChanges = AppUtils().getFileChanged("${AppConstants().DOCKER_IMAGE_FOLDER}/${AppConstants().DOCKER_TAG_FILE}")
          dockerFileChanges = AppUtils().getFileChanged("${AppConstants().DOCKER_IMAGE_FOLDER}/Dockerfile")
          if (versionFileChanges != '' && dockerFileChanges == '') {
            echo '#### WARNING: Version file was changed but there are no changes in Dockerfile!!'
            echo 'This will create a new docker image version that is equal to the previous version'
          }
          //do not allow overwrite of an existing docker image version
          if (versionFileChanges == '' && dockerFileChanges != '') {
            error '''Detected changes in Dockerfile but version was not changed!
                    You need to update the docker image version'''
          }
        }
      }
      post {
        success {
          script {
            AppUtils().githubStatus('prepare', 'success')
          }
        }
        unsuccessful {
          script {
            AppUtils().githubStatus('prepare', 'failure')
          }
        }
      }
    }
    stage('lint') {
      environment {
        HADOLINT_FAILURE_THRESHOLD = 'error'
      }
      when {
        expression { versionFileChanges != '' }
      }
      steps {
        script {
          //cannot use agent because of constraints in post call (requires curl)
          docker.withRegistry(AppConstants().DOCKER_REGISTRY, AppConstants().DOCKER_REGISTRY_CREDS) {
            docker.image('104553785803.dkr.ecr.us-west-2.amazonaws.com/hadolint/hadolint:v2.10.0-beta-alpine').inside {
              sh """
                hadolint ${AppConstants().DOCKER_IMAGE_FOLDER}/Dockerfile > hadolint_report.xml
                echo '###Docker lint results:'
                cat hadolint_report.xml
              """
            }
          }
        }
      }
      post {
        success {
          script {
            AppUtils().githubStatus('lint', 'success')
          }
        }
        unsuccessful {
          script {
            AppUtils().githubStatus('lint', 'failure')
          }
        }
      }
    }
    stage('build') {
      when {
        allOf {
          expression { versionFileChanges != '' }
          branch 'main'
        }
      }
      steps {
        script {
          docker.withRegistry(AppConstants().DOCKER_REGISTRY, AppConstants().DOCKER_REGISTRY_CREDS) {
            containersImg = docker.build(AppConstants().DOCKER_IMAGE_NAME, "./${AppConstants().DOCKER_IMAGE_FOLDER}")
            imgTag = AppUtils().dockerImgTag()
            echo "will create docker image with tag - ${imgTag}"
            containersImg.push('latest')
            containersImg.push(imgTag)
          }
        }
      }
      post {
        success {
          script {
            AppUtils().githubStatus('build', 'success')
          }
        }
        unsuccessful {
          script {
            AppUtils().githubStatus('build', 'failure')
          }
        }
      }
    }
  }
  post {
    success {
      script {
        currentBuild.result = 'SUCCESS'
      }
    }
    unsuccessful {
      script {
        currentBuild.result = 'FAILURE'
      }
    }
    cleanup {
      script {
        //clean created docker images
        if (containersImg) {
          sh("""
            set +x
            docker rmi -f ${AppConstants().DOCKER_IMAGE_NAME}:${imgTag}
            docker rmi -f ${AppConstants().DOCKER_IMAGE_NAME}:latest
          """)
        }
      }
      cleanWs()
    }
  }
}
