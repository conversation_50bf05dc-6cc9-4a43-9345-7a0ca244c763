# oclif/core Migration Checklist (v1.x → v4.x)

## 1. Update Dependency

- [ ] Update `@oclif/core` in `package.json` to `^4.4.0`
- [ ] Run `npm install` or `yarn install`

## 2. Review Code for Breaking Changes

- [ ] Check for deprecated/removed APIs (see changelog)
- [ ] Update any custom error handling, hooks, or config loading
- [ ] Ensure all commands use the correct import/require style
- [ ] Remove any usage of removed APIs (e.g., legacy hooks, deprecated flags)

## 3. Test CLI

- [ ] Run all CLI commands manually
- [ ] Run automated tests (if available)
- [ ] Check for runtime errors or warnings

## 4. Review oclif Plugins

- [ ] Ensure all oclif plugins are compatible with `@oclif/core@4.x`

## 5. Reference

- [ ] [oclif/core changelog](https://github.com/oclif/core/blob/main/CHANGELOG.md)
- [ ] [oclif/core migration guide](https://github.com/oclif/core/blob/main/MIGRATING.md)
