{"name": "client-container-tizen", "version": "6.4.202", "description": "Samsung Container for the Peacock / Classic Now App", "keywords": ["oclif"], "license": "MIT", "main": "src/index.js", "bin": {"build": "bin/run"}, "files": ["/bin", "/src"], "scripts": {"build": "./bin/run", "check-yarn-integrity": "yarn check --integrity", "precommit": "lint-staged", "commit": "git-cz -n", "debug": "node src/debug.js", "format": "prettier --write .", "lint": "eslint", "package": "node src/package.js", "prepare": "husky", "sideload": "node src/runSideload.js", "test": "jest"}, "dependencies": {"@oclif/core": "4.4.0", "minimist": "1.2.8", "xml-js": "1.6.11"}, "devDependencies": {"@eslint/eslintrc": "3.3.1", "@eslint/js": "9.29.0", "eslint": "9.29.0", "eslint-config-prettier": "10.1.5", "eslint-plugin-compat": "^6.0.2", "eslint-plugin-es5": "^1.5.0", "eslint-plugin-html": "8.1.3", "eslint-plugin-import": "2.31.0", "eslint-plugin-jest": "29.0.1", "eslint-plugin-promise": "7.2.1", "git-cz": "4.9.0", "globals": "16.2.0", "husky": "9.1.7", "jest": "30.0.2", "lint-staged": "16.1.2", "prettier": "3.5.3", "typescript": "5.8.3", "yarn-deduplicate": "6.0.2"}, "engines": {"node": "22.16.0"}, "oclif": {"bin": "build"}}