{"name": "client-container-tizen", "version": "6.4.202", "description": "Samsung Container for the Peacock / Classic Now App", "keywords": ["oclif"], "license": "MIT", "main": "src/index.js", "bin": {"build": "bin/run"}, "files": ["/bin", "/src"], "scripts": {"build": "./bin/run", "precommit": "lint-staged", "commit": "git-cz -n", "debug": "node src/debug.js", "format": "prettier --write src", "lint": "eslint src", "package": "node src/package.js", "prepare": "husky", "sideload": "node src/runSideload.js", "test": "jest"}, "lint-staged": {"*.{json,md}": ["prettier --write"], "*.{js,ts,html}": ["prettier --write", "eslint --fix"], "yarn.lock": "yarn-deduplicate"}, "dependencies": {"@oclif/core": "^1.7.0", "minimist": "^1.2.8", "xml-js": "^1.6.11"}, "devDependencies": {"@sky-uk/eslint-config-sky": "^33.3.0", "eslint": "^7.32.0", "eslint-config-prettier": "^7.2.0", "eslint-plugin-prettier": "^3.4.1", "git-cz": "^4.9.0", "husky": "^9.0.11", "jest": "^29.6.2", "lint-staged": "^15.2.7", "prettier": "^2.8.8", "yarn-deduplicate": "^6.0.2"}, "engines": {"node": ">=20.17"}, "oclif": {"bin": "build"}}