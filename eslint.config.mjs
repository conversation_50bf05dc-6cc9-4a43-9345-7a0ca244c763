import { defineConfig, globalIgnores } from 'eslint/config';
import prettierPlugin from 'eslint-plugin-prettier';
import jestPlugin from 'eslint-plugin-jest';
import globals from 'globals';
import path from 'node:path';
import { fileURLToPath } from 'node:url';
import js from '@eslint/js';
import { FlatCompat } from '@eslint/eslintrc';
import importPlugin from 'eslint-plugin-import';
import pluginPromise from 'eslint-plugin-promise';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const compat = new FlatCompat({
  baseDirectory: __dirname,
  recommendedConfig: js.configs.recommended,
  allConfig: js.configs.all,
});

export default defineConfig([
  globalIgnores(['.history/', 'bin/', 'node_modules/', 'dist/', 'resources/css/**/*.css']),
  ...compat.extends('prettier', 'plugin:prettier/recommended'),
  pluginPromise.configs['flat/recommended'],
  {
    plugins: {
      import: importPlugin,
      jest: jestPlugin,
      prettier: prettierPlugin,
      promise: pluginPromise,
    },

    languageOptions: {
      globals: {
        ...globals.node,
        ...globals.jest,
      },
    },

    rules: {
      'prettier/prettier': 'off',
    },
  },
]);
