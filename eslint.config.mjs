import js from '@eslint/js';
import globals from 'globals';
import htmlPlugin from 'eslint-plugin-html';
import importPlugin from 'eslint-plugin-import';
import jestPlugin from 'eslint-plugin-jest';
import prettierConfig from 'eslint-config-prettier';
import promisePlugin from 'eslint-plugin-promise';
import es5Plugin from 'eslint-plugin-es5';

const baseConfig = {
  languageOptions: {
    ...js.configs.recommended.languageOptions,
    globals: {
      console: 'readonly',
    },
  },
  plugins: {
    import: importPlugin,
    promise: promisePlugin,
  },
  rules: {
    ...js.configs.recommended.rules,
    ...importPlugin.flatConfigs.recommended.rules,
    ...promisePlugin.configs['flat/recommended'].rules,
    eqeqeq: ['error', 'always'],
    'no-console': 'error',
    'no-new-func': 'error',
    'no-new-wrappers': 'error',
    'no-param-reassign': 'error',
    'no-unused-vars': [
      'error',
      {
        vars: 'all',
        args: 'after-used',
        ignoreRestSiblings: false,
        argsIgnorePattern: '^_$', // Ignore parameters that are exactly underscore
        varsIgnorePattern: '^_$', // Ignore variables that are exactly underscore
        caughtErrorsIgnorePattern: '^_$', // Ignore caught errors that are exactly underscore
      },
    ],
  },
};

const es5Config = {
  ...baseConfig,
  plugins: {
    ...baseConfig.plugins,
    es5: es5Plugin,
  },
  languageOptions: {
    ...baseConfig.languageOptions,
    ecmaVersion: 5,
    sourceType: 'script',
    globals: {
      ...baseConfig.languageOptions.globals,
      document: 'readonly',
      tizen: 'readonly',
      webapis: 'readonly',
      window: 'readonly',
      XMLHttpRequest: 'readonly',
    },
  },
  rules: {
    ...baseConfig.rules,
    ...es5Plugin.configs['no-es2015'].rules,
    // Add specific ES6+ methods / Web APIs to ban using no-restricted-syntax
    'no-restricted-syntax': [
      'error',
      {
        selector:
          "CallExpression[callee.name='fetch'], CallExpression[callee.object.name='window'][callee.property.name='fetch']",
        message: 'fetch is not supported on Samsung 2016',
      },
      {
        selector:
          "NewExpression[callee.name='URL'], NewExpression[callee.object.name='window'][callee.property.name='URL']",
        message: 'URL is not supported on Samsung 2016',
      },
      {
        selector:
          "NewExpression[callee.name='Promise'], NewExpression[callee.object.name='window'][callee.property.name='Promise']",
        message: 'Promise is not supported on Samsung 2016',
      },
      {
        selector:
          "CallExpression[callee.object.name='Promise'], CallExpression[callee.object.object.name='window'][callee.object.property.name='Promise']",
        message: 'Promise is not supported on Samsung 2016',
      },
    ],
  },
};

export default [
  // 1. Global ignores
  {
    name: 'global:ignores',
    ignores: ['node_modules/', 'dist/', 'bin/', '.history/', 'resources/css/**/*.css'],
  },
  // 2. Configuration for the ESLint config file itself
  // This file uses ES module syntax (import/export)
  {
    name: 'config:eslint',
    files: ['eslint.config.mjs'],
    // Do NOT spread baseConfig here. baseConfig is for linted source files.
    // This file just needs to be parsed as an ES module.
    languageOptions: {
      ecmaVersion: 'latest',
      sourceType: 'module',
      globals: {
        ...globals.node,
      },
    },
  },
  // 3. Configuration for linting inline scripts in HTML files
  {
    name: 'source:html/inline-scripts',
    files: ['resources/html/**/*.html'],
    ...es5Config,
    plugins: {
      ...es5Config.plugins,
      html: htmlPlugin,
    },
  },
  // 4. Config for modern source code (src directory and root-level scripts)
  {
    // This glob targets .js files in `src` and the root directory.
    name: 'source:js/modern',
    files: ['src/**/*.js', '*.js'],
    ...baseConfig,
    languageOptions: {
      ...baseConfig.languageOptions,
      ecmaVersion: 'latest',
      sourceType: 'commonjs',
      globals: {
        ...baseConfig.languageOptions.globals,
        ...globals.node,
      },
    },
  },
  // 4a. Override for src/ to allow console logs
  // These are utilities that might need to log to the console during build/sideload processes.
  {
    name: 'override:src/allow-console',
    files: ['src/**/*.js'],
    rules: {
      'no-console': 'off',
    },
  },
  // 5. Legacy code intended for Samsung 2016+ devices (ES5 compatible).
  {
    name: 'source:js/samsung-2016-compat',
    files: ['resources/scripts/**/*.js'],
    ignores: [
      'resources/scripts/**/*.test.js',
      'resources/scripts/**/*.spec.js',
      'resources/scripts/**/__mocks__/**/*.js',
      'resources/scripts/**/__tests__/**/*.js',
      'resources/scripts/**/__test-utils__/**/*.js',
    ],
    ...es5Config,
  },
  // 6. Modern Tizens:
  // - the "Continue Watching" rail is supported starting from Tizen 4.0 (Samsung 2018).
  // - the "Preview" (Eden) rail is supported starting from Tizen 5.0 (Samsung 2019).
  {
    // TODO: consider moving both to resources/modules and resources/modules/utils
    name: 'source:js/samsung-2017-compat',
    files: ['resources/js/**/*.js', 'resources/utils/**/*.js'],
    ...baseConfig,
    languageOptions: {
      ...baseConfig.languageOptions,
      ecmaVersion: 2015, // Samsung 2017+ support ES6 / ES2015
      sourceType: 'commonjs',
      globals: {
        ...baseConfig.languageOptions.globals,
        tizen: 'readonly',
        webapis: 'readonly',
      },
    },
    rules: {
      ...baseConfig.rules,
      // Add specific ES6+ methods / Web APIs to ban using no-restricted-syntax
      'no-restricted-syntax': [
        'error',
        {
          selector:
            "NewExpression[callee.name='URL'], NewExpression[callee.object.name='window'][callee.property.name='URL']",
          message: 'URL is not supported on Samsung 2017 and very buggy on Samsung 2018',
        },
      ],
    },
  },
  // 7. Jest-specific configurations
  {
    name: 'test:jest',
    files: [
      'src/**/*.test.js',
      'src/**/__tests__/**/*.js',
      'src/**/__test-utils__/**/*.js',
      'src/**/__mocks__/**/*.js',
      'resources/scripts/**/*.test.js',
      'resources/scripts/**/__tests__/**/*.js',
      'resources/scripts/**/__test-utils__/**/*.js',
      'resources/scripts/**/__mocks__/**/*.js',
    ],
    ...baseConfig,
    languageOptions: {
      ...baseConfig.languageOptions,
      // Override the global ES5 setting for test files, which run in a modern Node.js environment.
      ecmaVersion: 'latest',
      sourceType: 'commonjs',
      globals: {
        ...baseConfig.languageOptions.globals,
        ...globals.jest,
        ...globals.node,
      },
    },
    plugins: {
      ...baseConfig.plugins,
      jest: jestPlugin,
    },
    rules: {
      ...baseConfig.rules,
      ...jestPlugin.configs['flat/recommended'].rules,
    },
  },
  // 8. Prettier configuration (MUST be the last one in the array)
  // This turns off all ESLint rules that are unnecessary or might conflict with Prettier.
  {
    name: 'plugin:prettier',
    ...prettierConfig,
  },
];
