import js from '@eslint/js';
import globals from 'globals';
import htmlPlugin from 'eslint-plugin-html';
import importPlugin from 'eslint-plugin-import';
import jestPlugin from 'eslint-plugin-jest';
import prettierConfig from 'eslint-config-prettier';
import promisePlugin from 'eslint-plugin-promise';
import compatPlugin from 'eslint-plugin-compat';
import es5Plugin from 'eslint-plugin-es5';

// The `eslint-plugin-import` package now supports flat config (`flatConfigs`).
// The previous method of spreading multiple config objects overwrites `rules`
// and other properties instead of merging them. This new `baseConfig` is
// constructed by correctly merging the pieces from recommended configs.
const baseConfig = {
  languageOptions: { ...js.configs.recommended.languageOptions },
  plugins: {
    import: importPlugin,
    promise: promisePlugin,
  },
  rules: {
    ...js.configs.recommended.rules,
    ...importPlugin.flatConfigs.recommended.rules,
    ...promisePlugin.configs['flat/recommended'].rules,
    'no-console': 'error',
    'no-unused-vars': [
      'error',
      {
        vars: 'all',
        args: 'after-used',
        ignoreRestSiblings: false,
        argsIgnorePattern: '^_$', // Ignore parameters that are exactly underscore
        varsIgnorePattern: '^_$', // Ignore variables that are exactly underscore
        caughtErrorsIgnorePattern: '^_$', // Ignore caught errors that are exactly underscore
      },
    ],
  },
};

export default [
  // 1. Global ignores
  {
    ignores: ['node_modules/', 'dist/', 'bin/', '.history/', 'resources/css/**/*.css'],
  },
  // 2. Configuration for the ESLint config file itself
  // This file uses ES module syntax (import/export)
  {
    files: ['eslint.config.mjs'],
    // Do NOT spread baseConfig here. baseConfig is for linted source files.
    // This file just needs to be parsed as an ES module.
    languageOptions: {
      ecmaVersion: 'latest',
      sourceType: 'module',
      globals: {
        ...globals.node,
      },
    },
  },
  // 3. Configuration for linting inline scripts in HTML files
  {
    files: ['resources/html/**/*.html'],
    ...baseConfig,
    plugins: {
      ...baseConfig.plugins,
      compat: compatPlugin,
      es5: es5Plugin,
      html: htmlPlugin,
    },
    rules: {
      ...baseConfig.rules,
      ...compatPlugin.configs['flat/recommended'].rules,
      ...es5Plugin.configs['no-es2015'].rules,
    },
    languageOptions: {
      ...baseConfig.languageOptions,
      ecmaVersion: 5,
      sourceType: 'script',
      globals: {
        document: 'readonly',
        tizen: 'readonly',
        webapis: 'readonly',
        window: 'readonly',
        XMLHttpRequest: 'readonly',
      },
    },
    settings: {
      compat: {
        // This environment should be defined in your .browserslistrc
        browserslistOpts: { env: 'samsung-2016' },
      },
    },
  },
  // 4. Config for modern source code (src directory and root-level scripts)
  {
    // This glob targets .js files in `src` and the root directory.
    // .mjs files are ES Modules and must have `sourceType: 'module'`, so they are not included here.
    files: ['src/**/*.js', '*.js', '!eslint.config.mjs'],
    ...baseConfig,
    languageOptions: {
      ...baseConfig.languageOptions,
      ecmaVersion: 'latest',
      sourceType: 'commonjs',
      globals: {
        ...globals.node,
      },
    },
  },
  // 4a. Override for src/ to allow console logs
  // These are utilities that might need to log to the console during build/sideload processes.
  {
    files: ['src/**/*.js'],
    rules: {
      'no-console': 'off',
    },
  },
  // 5. Legacy code intended for Samsung 2016+ devices (ES5 compatible).
  {
    files: ['resources/scripts/**/*.js'],
    ignores: [
      'resources/scripts/**/*.test.js',
      'resources/scripts/**/*.spec.js',
      'resources/scripts/**/__mocks__/**/*.js',
      'resources/scripts/**/__tests__/**/*.js',
      'resources/scripts/**/__test-utils__/**/*.js',
    ],
    ...baseConfig,
    plugins: {
      ...baseConfig.plugins,
      compat: compatPlugin,
      es5: es5Plugin,
    },
    languageOptions: {
      ...baseConfig.languageOptions,
      ecmaVersion: 5,
      sourceType: 'script',
      globals: {
        tizen: 'readonly',
        webapis: 'readonly',
        window: 'readonly',
        XMLHttpRequest: 'readonly',
      },
    },
    rules: {
      ...baseConfig.rules,
      ...compatPlugin.configs['flat/recommended'].rules,
      ...es5Plugin.configs['no-es2015'].rules,
    },
    settings: {
      compat: {
        // This environment should be defined in your .browserslistrc
        browserslistOpts: { env: 'samsung-2016' },
      },
    },
  },
  // 6. Modern Tizens:
  // - the "Continue Watching" rail is supported starting from Tizen 4.0 (Samsung 2018).
  // - the "Preview" (Eden) rail is supported starting from Tizen 5.0 (Samsung 2019).
  {
    // TODO: consider moving both to resources/modules and resources/modules/utils
    files: ['resources/js/**/*.js', 'resources/utils/**/*.js'],
    ...baseConfig,
    languageOptions: {
      ...baseConfig.languageOptions,
      ecmaVersion: 2015, // Samsung 2018+ supports native Promises (ES2015)
      sourceType: 'script',
      globals: {
        tizen: 'readonly',
        webapis: 'readonly',
      },
    },
    settings: {
      compat: {
        // This environment should be defined in your .browserslistrc
        browserslistOpts: { env: 'samsung-2017+' },
      },
    },
  },
  // 7. Jest-specific configuration
  {
    files: [
      '**/*.test.js',
      '**/*.spec.js',
      '**/__tests__/**/*.js',
      '**/__test-utils__/**/*.js',
      '**/__mocks__/**/*.js',
    ],
    // This configuration correctly merges the base configuration with Jest's recommended rules.
    // The previous implementation overwrote rules instead of combining them.
    languageOptions: {
      ...baseConfig.languageOptions,
      // Override the global ES5 setting for test files, which run in a modern Node.js environment.
      ecmaVersion: 'latest',
      sourceType: 'commonjs',
      globals: {
        ...globals.jest,
        ...globals.node,
        window: 'readonly',
      },
    },
    plugins: {
      ...baseConfig.plugins,
      jest: jestPlugin,
    },
    rules: {
      ...baseConfig.rules,
      ...jestPlugin.configs['flat/recommended'].rules,
    },
  },
  // 8. Prettier configuration (MUST be the last one in the array)
  // This turns off all ESLint rules that are unnecessary or might conflict with Prettier.
  prettierConfig,
];
