{"[css]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[javascript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[json]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "editor.formatOnSave": true, "editor.tabSize": 2, "files.insertFinalNewline": true, "files.trimFinalNewlines": true, "files.trimTrailingWhitespace": true, "eslint.workingDirectories": [{"pattern": "./"}], "css.validate": false, "css.format.spaceAroundSelectorSeparator": true, "editor.codeActionsOnSave": {"source.fixAll": "explicit", "source.fixAll.eslint": "explicit", "source.fixAll.fixAllJson": "explicit", "source.sortPackageJson": "explicit"}, "search.exclude": {".history/": true}}