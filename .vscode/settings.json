{"[html]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[javascript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[json]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "css.format.spaceAroundSelectorSeparator": true, "css.validate": false, "editor.codeActionsOnSave": {"source.fixAll": "explicit", "source.fixAll.eslint": "explicit", "source.fixAll.fixAllJson": true, "source.sortPackageJson": "explicit"}, "editor.formatOnSave": true, "editor.tabSize": 2, "eslint.workingDirectories": [{"pattern": "./"}], "files.insertFinalNewline": true, "files.trimFinalNewlines": true, "files.trimTrailingWhitespace": true, "jest.jestCommandLine": "source ~/.zshrc && nvm use && yarn test", "jest.shell": "/bin/zsh", "jest.runMode": {"type": "on-save", "testFile": true}, "search.exclude": {".history/": true}}