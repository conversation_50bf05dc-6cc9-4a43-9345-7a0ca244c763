const { Command, Flags } = require('@oclif/core');

const { build } = require('./build');

class TizenBuild extends Command {
  async run() {
    const { flags } = await this.parse(TizenBuild);
    await build(flags);
  }
}

TizenBuild.description = `Builds Tizen projects`;

TizenBuild.flags = {
  help: Flags.help({ char: 'h' }),
  version: Flags.version({ char: 'v' }),
  proposition: Flags.string({
    char: 'p',
    default: 'peacock',
    description: 'proposition to build - `nowott`, `skyshowtime`, `showmax`, or `peacock`',
  }),
  territory: Flags.string({
    char: 't',
    default: 'US',
    description: 'territory to build - `GB`, `IE`, `IT`, `DE`, `EU`, `AU`, or `US`',
  }),
  environment: Flags.string({
    char: 'e',
    default: 'production',
    description:
      'environment to build - `production`, `preview`, `rc`, `stable`, `stable-preview`, `proton`, `development`',
  }),
  featureflags: Flags.string({
    char: 'f',
    description: 'set featureflags - string separated by commas `"flag1, flag2"`',
  }),
  customURL: Flags.string({
    char: 'c',
    description: 'set a custom app URL',
  }),
  device: Flags.string({
    char: 'd',
    default: 'default',
    description: 'device version to build for - `default`, `oobe-voice`',
  }),
};

module.exports = TizenBuild;
