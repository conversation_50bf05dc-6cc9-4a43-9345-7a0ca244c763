const { execSync } = require('child_process');

const { getApplicationId, findDeviceConfig, extractTarget } = require('./utils/tizenCliUtils');

function debug() {
  const target = extractTarget();
  const config = findDeviceConfig(target);
  const { ip, port } = config;

  const applicationId = getApplicationId();

  try {
    // Older tizen versions require the timeout parameter
    const APP_LAUNCH_DEBUG_MODE_COMMAND = `sdb -s ${ip}:${port} shell 0 debug ${applicationId}`;
    const APP_LAUNCH_DEBUG_MODE_COMMAND_TIMEOUT = `${APP_LAUNCH_DEBUG_MODE_COMMAND} 300`;

    let result;
    try {
      result = execSync(APP_LAUNCH_DEBUG_MODE_COMMAND, {
        encoding: 'utf-8',
        stdio: 'pipe',
      });
    } catch (e) {
      result = execSync(APP_LAUNCH_DEBUG_MODE_COMMAND_TIMEOUT, {
        encoding: 'utf-8',
        stdio: 'pipe',
      });
    }

    console.log('result', result);

    if (result === null || result.includes('failed')) {
      throw new Error('Failed to launchDebugMode. Please check the application is already installed on the Target.');
    }

    const DEBUG_PORT = new RegExp(/(port(.*):\s+\d+)/g);
    const NUMBER_WORD = new RegExp(/\d+/);

    const debugPort = result.match(DEBUG_PORT)[0].match(NUMBER_WORD)[0];

    console.log(`\x1b[33mDEBUG URL: http://${ip}:${debugPort} \x1b[0m`);
  } catch (e) {
    console.log('Launching debug failed: ', e);
  }
}

debug();
