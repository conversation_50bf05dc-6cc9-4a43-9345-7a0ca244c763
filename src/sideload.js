const {
  exec,
  findPackage,
  PACKAGE_FOLDER,
  connectToDevice,
  extractTarget,
  findDeviceConfig,
  getApplicationId,
  shouldUninstall,
} = require('./utils/tizenCliUtils');

function sideload() {
  const target = extractTarget();
  const config = findDeviceConfig(target);
  const { ip, port } = config;

  const targetAddress = `${ip}:${port}`;
  connectToDevice(targetAddress);

  const name = findPackage('.wgt');

  if (shouldUninstall()) {
    const applicationId = getApplicationId();

    try {
      console.log('Uninstalling...');
      exec(`tizen uninstall --serial ${targetAddress} --pkgid "${applicationId}"`);
    } catch (err) {
      console.log(err);
    }
  }

  try {
    console.log('Installing...');
    exec(`tizen install --serial ${targetAddress} --name "${name}" -- ${PACKAGE_FOLDER}`);
  } catch (err) {
    console.log(err);
  }
}

module.exports = {
  sideload,
};
