# Test Utilities

This directory contains shared test utilities to improve maintainability and reduce duplicate code across test files.

## Mock Helpers

### `setupMockFs(fs, mockFiles)`

Sets up a comprehensive mock file system with the provided file structure.

**Parameters:**

- `fs` (Object): The mocked fs module (must have `__setMockFiles` method)
- `mockFiles` (Object): Object mapping file paths to their content or directory arrays

**Features:**

- Sets up mock implementations for `existsSync`, `readdirSync`, `statSync`, `readFileSync`
- Handles async fs functions (`writeFile`, `mkdir`) for output-data style tests
- Automatically filters out proposition/territory/environment directories from root resource scans
- Supports both file content and directory structures

**Example:**

```javascript
const { setupMockFs } = require('../../__test-utils__/mock-helpers');

beforeEach(() => {
  setupMockFs(fs, {
    '/path/to/file.txt': 'file content',
    '/path/to/directory': ['file1.txt', 'file2.txt'],
  });
});
```

### `clearMockFs(fs)`

Clears all mock fs state and call history.

**Parameters:**

- `fs` (Object): The mocked fs module

**Features:**

- Resets mock files state using `__setMockFiles({})`
- Clears call history for all fs functions
- Safe to call even if fs functions don't exist

**Example:**

```javascript
const { clearMockFs } = require('../../__test-utils__/mock-helpers');

beforeEach(() => {
  clearMockFs(fs);
});
```

### `setupMockVariableResolvers(mockVariableResolvers, defaultValues)`

Sets up mock variable resolvers with default return values.

**Parameters:**

- `mockVariableResolvers` (Object): Object containing mock resolver functions
- `defaultValues` (Object): Object mapping resolver names to their default return values

**Features:**

- Clears call history for all resolvers
- Sets up default return values
- Uses fallback pattern `RESOLVED_${resolverName}` if no default provided

**Example:**

```javascript
const { setupMockVariableResolvers } = require('../../__test-utils__/mock-helpers');

const mockVariableResolvers = {
  VARIABLE_1: jest.fn(),
  VARIABLE_2: jest.fn(),
};

beforeEach(() => {
  setupMockVariableResolvers(mockVariableResolvers, {
    VARIABLE_1: 'RESOLVED_VALUE',
    VARIABLE_2: 'RESOLVED_VALUE_2',
  });
});
```

## Usage Patterns

### File System Tests (get-resources style)

```javascript
jest.mock('fs');

const fs = require('fs');
const { setupMockFs, clearMockFs } = require('../../__test-utils__/mock-helpers');

beforeEach(() => {
  clearMockFs(fs);
});

test('should work with mock files', () => {
  setupMockFs(fs, {
    '/path/to/file.txt': 'content',
  });

  // Your test code here
});
```

### Async File System Tests (output-data style)

```javascript
jest.mock('fs');

const fs = require('fs');
const { setupMockFs } = require('../../__test-utils__/mock-helpers');

beforeEach(() => {
  // setupMockFs automatically handles async fs functions
  setupMockFs(fs);
});

test('should work with async fs operations', async () => {
  // Your async test code here
});
```

### Variable Resolver Tests (apply-variables style)

```javascript
const { setupMockVariableResolvers } = require('../../__test-utils__/mock-helpers');

const mockVariableResolvers = {
  VARIABLE_1: jest.fn(),
  VARIABLE_2: jest.fn(),
};

beforeEach(() => {
  setupMockVariableResolvers(mockVariableResolvers, {
    VARIABLE_1: 'RESOLVED_VALUE',
    VARIABLE_2: 'RESOLVED_VALUE_2',
  });
});
```

## Benefits

1. **Consistency**: All tests use the same mock setup patterns
2. **Maintainability**: Changes to mock behavior only need to be made in one place
3. **Reduced Duplication**: No need to copy-paste mock implementations
4. **Jest Randomization Ready**: All helpers are designed to work with randomized test execution
5. **Type Safety**: Clear parameter validation and error messages
