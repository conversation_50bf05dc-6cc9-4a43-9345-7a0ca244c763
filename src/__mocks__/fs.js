// __mocks__/fs.js

'use strict';

const path = require('path');

const fs = jest.genMockFromModule('fs');

// This is a custom function that our tests can use during setup to specify
// what the files on the "mock" filesystem should look like when any of the
// `fs` APIs are used.
let mockFiles = Object.create(null);
function __setMockFiles(newMockFiles) {
  mockFiles = Object.create(null);
  for (const file in newMockFiles) {
    if (Object.prototype.hasOwnProperty.call(newMockFiles, file)) {
      const dir = path.dirname(file);

      if (!mockFiles[dir]) {
        mockFiles[dir] = [];
      }
      mockFiles[dir].push(path.basename(file));
      mockFiles[file] = newMockFiles[file];
    }
  }
}

function readFileSync(filePath) {
  return mockFiles[filePath];
}

function readdirSync(directoryPath) {
  return mockFiles[directoryPath] || [];
}

function existsSync(filePath) {
  return mockFiles[filePath] !== undefined;
}

function statSync(filePath) {
  return {
    isDirectory: () => Array.isArray(mockFiles[filePath]),
  };
}

function writeFile(filePath, file, cb) {
  mockFiles[filePath] = file;
  cb();
}

fs.__setMockFiles = __setMockFiles;
fs.statSync = jest.fn(statSync);
fs.readFileSync = jest.fn(readFileSync);
fs.readdirSync = jest.fn(readdirSync);
fs.existsSync = jest.fn(existsSync);
fs.writeFile = jest.fn(writeFile);

module.exports = fs;
