// __mocks__/fs.js

'use strict';

const path = require('path');

const fs = jest.createMockFromModule('fs');

// This is a custom function that our tests can use during setup to specify
// what the files on the "mock" filesystem should look like when any of the
// `fs` APIs are used.
let mockFiles = Object.create(null);
function __setMockFiles(newMockFiles) {
  mockFiles = Object.create(null);

  for (const file in newMockFiles) {
    if (Object.prototype.hasOwnProperty.call(newMockFiles, file)) {
      // Set the file content
      mockFiles[file] = newMockFiles[file];

      // Create all intermediate directories
      let currentPath = file;
      const pathParts = [];

      // Build the path hierarchy from file to root
      while (currentPath !== path.dirname(currentPath)) {
        const dir = path.dirname(currentPath);
        const basename = path.basename(currentPath);
        pathParts.unshift({ dir, basename });
        currentPath = dir;
      }

      // Create directory entries for all intermediate paths
      for (const { dir, basename } of pathParts) {
        if (!mockFiles[dir]) {
          mockFiles[dir] = [];
        }
        if (!mockFiles[dir].includes(basename)) {
          mockFiles[dir].push(basename);
        }
      }
    }
  }
}

function readFileSync(filePath) {
  return mockFiles[filePath];
}

function readdirSync(directoryPath) {
  const files = mockFiles[directoryPath] || [];

  // Filter out proposition/territory/environment directories from root resource scans
  // This prevents cross-contamination between different resource hierarchy levels
  if (
    directoryPath.match(/resources\/[^/]+$/) &&
    !directoryPath.includes('/proposition/') &&
    !directoryPath.includes('/territory/')
  ) {
    // This is a root resource directory, filter out hierarchy directories
    return files.filter((file) => !['proposition', 'territory', 'environment'].includes(file));
  }

  return files;
}

function existsSync(filePath) {
  return mockFiles[filePath] !== undefined;
}

function statSync(filePath) {
  return {
    isDirectory: () => Array.isArray(mockFiles[filePath]),
  };
}

function writeFile(filePath, file, cb) {
  mockFiles[filePath] = file;
  cb();
}

fs.__setMockFiles = __setMockFiles;
fs.statSync = jest.fn(statSync);
fs.readFileSync = jest.fn(readFileSync);
fs.readdirSync = jest.fn(readdirSync);
fs.existsSync = jest.fn(existsSync);
fs.writeFile = jest.fn(writeFile);

module.exports = fs;
