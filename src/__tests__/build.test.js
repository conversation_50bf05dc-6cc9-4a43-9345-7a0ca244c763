const fs = require('fs');
const path = require('path');

const proposition = 'proposition';
const territory = 'territory';
const environment = 'environment';
const featureflags = undefined;
const customURL = undefined;
const url = 'url/';
const device = 'oobe-voice';
const options = { proposition, territory, environment, device };
const mockMergedConfig = {
  url,
  customURL,
  environment,
  featureflags,
  clientToken: 'dW5kZWZpbmVkOu+/vXdefinvv70=',
  splashScreen: '&quot;data:image/png;base64,aW1hZ2Vz&quot;',
  css: {
    fontRegular: 'regular_font.woff',
    fontBold: 'bold_font.woff',
    retryButtonColor: '#ffe000',
    retryButtonTextColor: 'black',
  },
};
const expectedConfig = { ...mockMergedConfig };

const outputDir = path.join(process.cwd(), 'dist');

jest
  .mock('../utils/merge-config-variables', () => ({
    mergeConfigVariables: jest.fn(() => mockMergedConfig),
  }))
  .mock('../utils/apply-variables', () => ({
    applyVariables: jest.fn((variables, files, config = {}) => {
      if (config.isFolder) {
        return { variablesApplied: { variables, files } };
      }

      return { variablesApplied: { variables, file: files } };
    }),
  }))
  .mock('../utils/output-data', () => ({ outputData: jest.fn() }))
  .mock('../utils/get-resources', () => ({
    getBaseConfigXML: jest.fn(() => 'configXML'),
    getMergedResourceFiles: jest.fn((type) => {
      if (type === 'images') {
        return { merged: type, 'splashScreen.png': type };
      }

      return { merged: type };
    }),
  }))
  .mock('fs');

const consoleLogSpy = jest.spyOn(console, 'log').mockImplementation(() => {});

const { mergeConfigVariables } = require('../utils/merge-config-variables');
const { outputData } = require('../utils/output-data');
const { build } = require('../build');
const { getMergedResourceFiles } = require('../utils/get-resources');
const { applyVariables } = require('../utils/apply-variables');

afterEach(() => {
  expect(consoleLogSpy).toHaveBeenCalledTimes(1);
  expect(consoleLogSpy).toHaveBeenCalledWith(
    'Built:',
    'url/?container=tizen&container_version=undefined'
  );
});

test('should mergeConfigVariables', async () => {
  await build(options);
  expect(mergeConfigVariables).toHaveBeenCalledTimes(1);
  expect(mergeConfigVariables).toHaveBeenCalledWith(proposition, territory, environment, device);
});

test('should getMergedResourceFiles', async () => {
  await build(options);
  expect(getMergedResourceFiles).toHaveBeenCalledTimes(7);
  // Check non-recursive calls
  ['html', 'css', 'fonts', 'images', 'utils', 'project'].forEach((type) => {
    expect(getMergedResourceFiles).toHaveBeenCalledWith(type, proposition, territory, environment);
  });
  // Check recursive call for 'js'
  expect(getMergedResourceFiles).toHaveBeenCalledWith(
    'js',
    proposition,
    territory,
    environment,
    true
  );
});

test('should apply resolved variables to html, css, js and utils files', async () => {
  await build(options);
  expect(applyVariables).toHaveBeenCalledTimes(5);

  const { css, ...expectHTMLConfig } = expectedConfig;

  expect(applyVariables).toHaveBeenNthCalledWith(2, css, { merged: 'css' }, { isFolder: true });
  expect(applyVariables).toHaveBeenNthCalledWith(
    3,
    expectHTMLConfig,
    { merged: 'html' },
    { isFolder: true }
  );
  expect(applyVariables).toHaveBeenNthCalledWith(
    4,
    expectedConfig,
    { merged: 'js' },
    { isFolder: true, recursive: true }
  );
  expect(applyVariables).toHaveBeenNthCalledWith(
    5,
    expectedConfig,
    { merged: 'utils' },
    { isFolder: true }
  );
});

test('should apply resolved variables to config.xml', async () => {
  await build(options);
  expect(applyVariables).toHaveBeenCalledTimes(5);
  expect(applyVariables).toHaveBeenCalledWith(expectedConfig, 'configXML');
});

test('should remove existing build', async () => {
  await build(options);
  expect(fs.rmSync).toHaveBeenCalledTimes(1);
  expect(fs.rmSync).toHaveBeenCalledWith(outputDir, { force: true, recursive: true });
});

test('should output data', async () => {
  await build(options);

  const { css, ...expectHTMLConfig } = expectedConfig;

  expect(outputData).toHaveBeenCalledTimes(1);
  expect(outputData).toHaveBeenCalledWith(outputDir, {
    'config.xml': {
      variablesApplied: {
        file: 'configXML',
        variables: expectedConfig,
      },
    },
    html: {
      variablesApplied: {
        files: { merged: 'html' },
        variables: expectHTMLConfig,
      },
    },
    css: {
      variablesApplied: {
        files: { merged: 'css' },
        variables: css,
      },
    },
    fonts: { merged: 'fonts' },
    images: { merged: 'images' },
    js: {
      variablesApplied: {
        files: { merged: 'js' },
        variables: expectedConfig,
      },
    },
    merged: 'project',
    utils: {
      variablesApplied: {
        files: { merged: 'utils' },
        variables: expectedConfig,
      },
    },
  });
});
