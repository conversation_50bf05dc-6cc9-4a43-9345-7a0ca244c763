const fs = require('fs');

const { exec, findPackage, PACKAGE_FOLDER } = require('./utils/tizenCliUtils');

function packageTizen() {
  exec(`tizen package --type wgt -- ${PACKAGE_FOLDER}`);

  const packageName = findPackage('.wgt');
  const compatibleName = packageName.replace(/\s/g, '');

  if (packageName !== compatibleName) {
    console.info(`Renaming package from ${packageName} -> ${compatibleName}`);
    fs.rename(`${PACKAGE_FOLDER}/${packageName}`, `${PACKAGE_FOLDER}/${compatibleName}`, (err) => {
      if (err) {
        throw err;
      }
      console.log('Successfully renamed - AKA moved!');
    });
  }

  console.info(`Created package [${PACKAGE_FOLDER}/${compatibleName}]`);
}

packageTizen();
