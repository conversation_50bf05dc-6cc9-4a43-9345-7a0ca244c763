const variableResolvers = require('./variable-resolvers');

const defaultOptions = { isFolder: false, recursive: false };

function defaultResolver(configVariables) {
  return Object.keys(configVariables).reduce((obj, variable) => {
    obj[variable] = (config) => config[variable];
    return obj;
  }, {});
}

function generateVariableResolvers(configVariables) {
  return {
    ...defaultResolver(configVariables),
    ...variableResolvers,
  };
}
function validateFile(fileData) {
  const unresolvedVariables = fileData.match(/\${.+}/g);

  if (unresolvedVariables) {
    unresolvedVariables.forEach((variable) => {
      console.error(`ERROR: Unresolved variable`, variable.substring(2, variable.length - 1));
    });
  }

  return fileData;
}

function replaceVariableInFile({ variable, value, file }) {
  return file.toString().replace(new RegExp(`\\$\{${variable}}`, 'g'), value);
}

function applyVariablesToFile(configVariables, initialFile) {
  const generatedVariableResolvers = generateVariableResolvers(configVariables);
  const updatedFile = Object.entries(generatedVariableResolvers).reduce(
    (file, [variable, resolver]) => {
      const value = resolver(configVariables);

      // If value is null or undefined, the variable should be erased from the file
      if (!value) return replaceVariableInFile({ variable, value: '', file });

      return replaceVariableInFile({ variable, value, file });
    },
    initialFile
  );

  return validateFile(updatedFile);
}

function applyVariables(configVariables, file, options = defaultOptions) {
  if (!options.isFolder) {
    return applyVariablesToFile(configVariables, file);
  }

  const updatedFiles = {};
  for (const fileName in file) {
    if (file.hasOwnProperty(fileName)) {
      const fileData = file[fileName];
      const isObject =
        fileData &&
        typeof fileData === 'object' &&
        !(typeof Buffer !== 'undefined' && Buffer.isBuffer && Buffer.isBuffer(fileData));
      if (isObject && options.recursive) {
        // Recursively apply to subdirectory if recursive is true
        const newOptions = {};
        for (const key in options) {
          if (options.hasOwnProperty(key)) {
            newOptions[key] = options[key];
          }
        }
        newOptions.isFolder = true;
        updatedFiles[fileName] = applyVariables(configVariables, fileData, newOptions);
      } else if (isObject && !options.recursive) {
        // Ignore directories if not recursive
        continue;
      } else {
        // Apply to file
        updatedFiles[fileName] = applyVariablesToFile(configVariables, fileData);
      }
    }
  }
  return updatedFiles;
}

module.exports = { applyVariables };
