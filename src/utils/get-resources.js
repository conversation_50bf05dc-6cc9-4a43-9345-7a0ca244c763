const fs = require('fs');
const path = require('path');

function getFiles(folderPath) {
  if (!fs.existsSync(folderPath)) {
    return;
  }
  const results = fs.readdirSync(folderPath);
  const files = results.filter((file) => !fs.statSync(path.join(folderPath, file)).isDirectory());
  return files.reduce((fileData, file) => {
    fileData[file] = fs.readFileSync(path.join(folderPath, file));
    return fileData;
  }, {});
}

function getResourceFiles(resource) {
  return getFiles(path.join(process.cwd(), `resources/${resource}`));
}

function getPropositionFiles(resource, proposition) {
  return getFiles(path.join(process.cwd(), `resources/${resource}/${proposition}`));
}

function getTerritoryFiles(resource, proposition, territory) {
  return getFiles(path.join(process.cwd(), `resources/${resource}/${proposition}/${territory}`));
}

function getTerritoryEnvironmentFiles(resource, proposition, territory, environment) {
  return getFiles(path.join(process.cwd(), `resources/${resource}/${proposition}/${territory}/${environment}`));
}

function getMergedResourceFiles(resource, proposition, territory, environment) {
  const resourceFiles = getResourceFiles(resource);
  const propositionFiles = getPropositionFiles(resource, proposition);
  const territoryFiles = getTerritoryFiles(resource, proposition, territory);
  const territoryEnvironmentFiles = getTerritoryEnvironmentFiles(resource, proposition, territory, environment);

  return {
    ...resourceFiles,
    ...propositionFiles,
    ...territoryFiles,
    ...territoryEnvironmentFiles,
  };
}

function getBaseConfigXML() {
  return fs.readFileSync(path.join(process.cwd(), 'resources/config.xml'), 'utf-8');
}

module.exports = { getMergedResourceFiles, getBaseConfigXML };
