const fs = require('fs');
const path = require('path');

function getFiles(folderPath, recursive) {
  if (!fs.existsSync(folderPath)) {
    return;
  }
  const results = fs.readdirSync(folderPath);
  return results.reduce((fileData, file) => {
    const fullPath = path.join(folderPath, file);
    if (fs.statSync(fullPath).isDirectory()) {
      if (recursive) {
        fileData[file] = getFiles(fullPath, true); // Recursively get files in subdirectory
      }
      // If not recursive, skip directories
    } else {
      fileData[file] = fs.readFileSync(fullPath);
    }
    return fileData;
  }, {});
}

function getResourceFiles(resource, recursive) {
  return getFiles(path.join(process.cwd(), `resources/${resource}`), recursive);
}

function getPropositionFiles(resource, proposition, recursive) {
  return getFiles(path.join(process.cwd(), `resources/${resource}/${proposition}`), recursive);
}

function getTerritoryFiles(resource, proposition, territory, recursive) {
  return getFiles(
    path.join(process.cwd(), `resources/${resource}/${proposition}/${territory}`),
    recursive
  );
}

function getTerritoryEnvironmentFiles(resource, proposition, territory, environment, recursive) {
  return getFiles(
    path.join(process.cwd(), `resources/${resource}/${proposition}/${territory}/${environment}`),
    recursive
  );
}

function getMergedResourceFiles(resource, proposition, territory, environment, recursive) {
  const resourceFiles = getResourceFiles(resource, recursive);
  const propositionFiles = getPropositionFiles(resource, proposition, recursive);
  const territoryFiles = getTerritoryFiles(resource, proposition, territory, recursive);
  const territoryEnvironmentFiles = getTerritoryEnvironmentFiles(
    resource,
    proposition,
    territory,
    environment,
    recursive
  );

  return {
    ...resourceFiles,
    ...propositionFiles,
    ...territoryFiles,
    ...territoryEnvironmentFiles,
  };
}

function getBaseConfigXML() {
  return fs.readFileSync(path.join(process.cwd(), 'resources/config.xml'), 'utf-8');
}

module.exports = { getMergedResourceFiles, getBaseConfigXML };
