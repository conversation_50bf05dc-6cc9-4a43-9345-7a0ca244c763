const fs = require('fs');
const childProcess = require('child_process');

const minimist = require('minimist');
const convert = require('xml-js');

let devices = [];
try {
  // eslint-disable-next-line import/no-unresolved
  devices = require('../devices.json');
} catch (ex) {
  throw new Error(
    '\x1b[31mCannot find src/devices.json. Make sure it is copied from the example file.\x1b[0m'
  );
}

const PACKAGE_FOLDER = 'dist';

function exit(msg) {
  console.error(msg); // eslint-disable-line no-console
  process.exit(-1);
}

function exec(cmd) {
  childProcess.execSync(`${cmd}`, {
    stdio: [0, 1, 2],
  });
}

function findPackage(extension) {
  if (!fs.existsSync(PACKAGE_FOLDER)) {
    exit('Package folder does not exist. Run `yarn build`');
  }

  const files = fs.readdirSync(PACKAGE_FOLDER);
  const packages = files.filter((file) => file.endsWith(extension));
  if (packages.length === 0) {
    exit(`No package was found in [${PACKAGE_FOLDER}]`);
  } else if (packages.length > 1) {
    exit(`Multiple packages were found in [${PACKAGE_FOLDER}]`);
  }

  return packages[0];
}

function getApplicationId() {
  if (!fs.existsSync(PACKAGE_FOLDER)) {
    exit('Package folder does not exist. Run `yarn build`');
  }

  const xml = fs.readFileSync(`${PACKAGE_FOLDER}/config.xml`, 'utf8');

  const xmlAsJson = JSON.parse(convert.xml2json(xml));
  const xmlElements = xmlAsJson.elements[0].elements;
  const appId = xmlElements.find((ele) => ele.name === 'tizen:application').attributes.id;

  return appId;
}

function connectToDevice(targetAddress) {
  try {
    console.log('Connecting...');
    exec(`sdb connect ${targetAddress}`);
  } catch (ex) {
    throw new Error(`Cannot connect to device ${targetAddress}.`);
  }
}

function extractTarget() {
  const argv = minimist(process.argv.slice(2));
  return argv._[0];
}

function shouldUninstall() {
  return process.argv.includes('--uninstall') || process.argv.includes('-u');
}

function findDeviceConfig(target) {
  const stringTarget = target.toString();
  const device = devices.find(
    (d) => d.id === stringTarget || (d.aliases && d.aliases.includes(stringTarget))
  );
  if (device) {
    return device;
  }

  if (!device) {
    throw new Error(
      `No configuration found for target "${target}". Make sure it is configured in "devices.json".`
    );
  }
}

module.exports = {
  exec,
  findPackage,
  exit,
  PACKAGE_FOLDER,
  connectToDevice,
  getApplicationId,
  extractTarget,
  findDeviceConfig,
  shouldUninstall,
};
