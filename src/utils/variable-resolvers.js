class URL {
  constructor(config) {
    this.config = config;
    this.url = {
      base: config.customURL || config.url,
      params: [],
    };
  }

  addParams(param) {
    this.url.params.push(param);
  }

  buildURL() {
    const { query, featureflags, version } = this.config;

    if (query) {
      this.addParams(query);
    }

    if (featureflags) {
      featureflags.split(', ').forEach((feature) => this.addParams(`FLAG__${feature}=enabled`));
    }

    this.addParams(`container=tizen&container_version=${version}`);

    return this;
  }

  toString() {
    const { base, params } = this.url;

    const paramsString = params.length ? `?${params.join('&')}` : '';

    return `${base}${paramsString}`;
  }
}

const createUrl = (config) => new URL(config).buildURL().toString();

const escapeUrl = (url) => url.replace(/&/g, '&amp;');

module.exports = {
  appNamespace: (config) => config.appNamespace || '',
  url: (config) => createUrl(config),
  escapedUrl: (config) => escapeUrl(createUrl(config)),
  edenPreviewServiceId: (config) =>
    config.applicationId ? `${config.applicationId.split('.')[0]}.service` : '',
  continueWatchingServiceId: (config) =>
    config.applicationId ? `${config.applicationId.split('.')[0]}.ContinueWatchingService` : '',
  packageId: (config) => (config.applicationId ? config.applicationId.split('.')[0] : ''),
  applicationName: (config) => (config.applicationId ? config.applicationId.split('.')[1] : ''),
  newRelicApiKey: (config) => config.newRelicApiKey || '',
  voiceOnSearch: (config) =>
    config.voiceOnSearchKey
      ? config.voiceOnSearch.replace('${voiceOnSearchKey}', config.voiceOnSearchKey)
      : '',
  splashZoom: (config) => config.splashZoom ?? 100,
};
