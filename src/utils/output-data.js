const fs = require('fs');
const path = require('path');

function outputFile(filePath, file) {
  return new Promise((resolve, reject) => {
    fs.writeFile(filePath, file, (err) => {
      if (err) {
        return reject(err);
      }
      resolve();
    });
  });
}

function outputFolder(filePath, folder) {
  return new Promise((resolve, reject) => {
    fs.mkdir(filePath, (err) => {
      if (err) {
        return reject(err);
      }

      const files = Object.entries(folder).map(([file, data]) =>
        outputData(path.join(filePath, file), data)
      );
      Promise.all(files).then(resolve).catch(reject);
    });
  });
}

function outputData(filePath, data) {
  if (typeof data === 'string' || data instanceof Buffer) {
    return outputFile(filePath, data);
  } else if (typeof data === 'object') {
    return outputFolder(filePath, data);
  }
}

module.exports = { outputData };
