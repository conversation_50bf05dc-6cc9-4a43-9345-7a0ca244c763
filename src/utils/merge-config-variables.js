const path = require('path');

function getConfigFile(name) {
  try {
    return require(name);
  } catch (e) {
    if (e.code === 'MODULE_NOT_FOUND') {
      return {};
    }
    throw e;
  }
}

function getPropositionConfig(proposition) {
  const config = getConfigFile(
    path.join(process.cwd(), `build-variables/${proposition}/build-variables.json`)
  );
  if (Object.keys(config).length !== 0) {
    return config;
  } else {
    throw console.error(`ERR: couldn't get config for proposition ${proposition}`);
  }
}

function getTerritoryConfig(proposition, territory) {
  return getConfigFile(
    path.join(process.cwd(), `build-variables/${proposition}/${territory}/build-variables.json`)
  );
}

function getEnvironmentConfig(proposition, environment) {
  const buildVariables = getConfigFile(
    path.join(process.cwd(), `build-variables/${proposition}/build-variables.json`)
  );

  return buildVariables.environments && buildVariables.environments[environment];
}

function getTerritoryEnvironmentConfig(proposition, territory, environment) {
  const buildVariables = getConfigFile(
    path.join(process.cwd(), `build-variables/${proposition}/${territory}/build-variables.json`)
  );

  return buildVariables && buildVariables.environments && buildVariables.environments[environment];
}

function getDeviceConfig(device) {
  return getConfigFile(
    path.join(process.cwd(), `device-config-variables/${device}/device-config-variables.json`)
  );
}

function mergeConfigVariables(proposition, territory, environment, device) {
  const propositionConfig = getPropositionConfig(proposition);
  const territoryConfig = getTerritoryConfig(proposition, territory);
  const environmentConfig = getEnvironmentConfig(proposition, environment);
  const territoryEnvironmentConfig = getTerritoryEnvironmentConfig(
    proposition,
    territory,
    environment
  );
  const deviceConfig = getDeviceConfig(device);

  return {
    ...propositionConfig,
    ...territoryConfig,
    ...environmentConfig,
    ...territoryEnvironmentConfig,
    ...deviceConfig,
  };
}

module.exports = { mergeConfigVariables };
