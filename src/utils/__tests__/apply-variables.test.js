const mockVariableResolvers = {
  VARIABLE_1: jest.fn(() => 'RESOLVED_VALUE'),
  VARIABLE_2: jest.fn(() => 'RESOLVED_VALUE_2'),
};
jest.mock('../variable-resolvers', () => mockVariableResolvers);

const { applyVariables } = require('../apply-variables');

const mockConfig = { config: 'data' };

const file = '${config}XMLwith${VARIABLE_1}and${VARIABLE_2}';
const fileWithUnresolvableVariable = '${config}XMLwith${UNRESOLVABLE_VARIABLE}';

const files = {
  file,
  fileWithUnresolvableVariable,
};

let consoleErrorSpy;

beforeEach(() => {
  consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
});

afterEach(() => {
  jest.restoreAllMocks();
});

describe('applyVariables', () => {
  test('should call variable resolvers', async () => {
    await applyVariables(mockConfig, file);
    expect(mockVariableResolvers.VARIABLE_1).toHaveBeenCalledTimes(1);
    expect(mockVariableResolvers.VARIABLE_1).toHaveBeenCalledWith(mockConfig);
    expect(mockVariableResolvers.VARIABLE_2).toHaveBeenCalledTimes(1);
    expect(mockVariableResolvers.VARIABLE_2).toHaveBeenCalledWith(mockConfig);
  });

  test('should replace all variables with result of variable resolver', async () => {
    const result = await applyVariables(mockConfig, file);
    expect(result).toEqual('dataXMLwithRESOLVED_VALUEandRESOLVED_VALUE_2');
  });

  test('should not replace variable if resolves to undefined', async () => {
    const result = await applyVariables(mockConfig, fileWithUnresolvableVariable);
    expect(result).toEqual('dataXMLwith${UNRESOLVABLE_VARIABLE}');
    expect(consoleErrorSpy).toHaveBeenCalledTimes(1);
    expect(consoleErrorSpy).toHaveBeenCalledWith(
      'ERROR: Unresolved variable',
      'UNRESOLVABLE_VARIABLE'
    );
  });
});

describe('applyVariables (folder)', () => {
  test('should replace all variables with result of variable resolver', async () => {
    await applyVariables(mockConfig, files, { isFolder: true });
    expect(mockVariableResolvers.VARIABLE_1).toHaveBeenCalledTimes(2);
    expect(mockVariableResolvers.VARIABLE_1).toHaveBeenCalledWith(mockConfig);
    expect(mockVariableResolvers.VARIABLE_2).toHaveBeenCalledTimes(2);
    expect(mockVariableResolvers.VARIABLE_2).toHaveBeenCalledWith(mockConfig);
  });

  test('should applyResolvedVariablesToFile for each file', async () => {
    const result = await applyVariables(mockConfig, files, { isFolder: true });
    expect(result).toEqual({
      file: 'dataXMLwithRESOLVED_VALUEandRESOLVED_VALUE_2',
      fileWithUnresolvableVariable: 'dataXMLwith${UNRESOLVABLE_VARIABLE}',
    });
    expect(consoleErrorSpy).toHaveBeenCalledTimes(1);
    expect(consoleErrorSpy).toHaveBeenCalledWith(
      'ERROR: Unresolved variable',
      'UNRESOLVABLE_VARIABLE'
    );
  });
});

describe('applyVariables (folder) (recursive)', () => {
  const nestedFiles = {
    file,
    fileWithUnresolvableVariable,
    subdir: {
      nestedFile: file,
      nestedUnresolvable: fileWithUnresolvableVariable,
    },
  };

  test('should recursively replace all variables with result of variable resolver', async () => {
    await applyVariables(mockConfig, nestedFiles, { isFolder: true, recursive: true });
    // VARIABLE_1 and VARIABLE_2 should be called once for each file containing them (file + subdir.nestedFile)
    expect(mockVariableResolvers.VARIABLE_1).toHaveBeenCalledTimes(4);
    expect(mockVariableResolvers.VARIABLE_2).toHaveBeenCalledTimes(4);
  });

  test('should recursively applyResolvedVariablesToFile for each file', async () => {
    const result = await applyVariables(mockConfig, nestedFiles, {
      isFolder: true,
      recursive: true,
    });
    expect(result).toEqual({
      file: 'dataXMLwithRESOLVED_VALUEandRESOLVED_VALUE_2',
      fileWithUnresolvableVariable: 'dataXMLwith${UNRESOLVABLE_VARIABLE}',
      subdir: {
        nestedFile: 'dataXMLwithRESOLVED_VALUEandRESOLVED_VALUE_2',
        nestedUnresolvable: 'dataXMLwith${UNRESOLVABLE_VARIABLE}',
      },
    });
    // Should log unresolved variable for both top-level and nested unresolvable files
    expect(consoleErrorSpy).toHaveBeenCalledTimes(2);
    expect(consoleErrorSpy).toHaveBeenCalledWith(
      'ERROR: Unresolved variable',
      'UNRESOLVABLE_VARIABLE'
    );
  });

  test('should ignore subdirectories when recursive is false', async () => {
    const filesWithSubdir = {
      ...files,
      subdir: {
        nestedFile: file,
        nestedUnresolvable: fileWithUnresolvableVariable,
      },
    };
    const result = await applyVariables(mockConfig, filesWithSubdir, {
      isFolder: true,
      recursive: false,
    });
    expect(result).toEqual({
      file: 'dataXMLwithRESOLVED_VALUEandRESOLVED_VALUE_2',
      fileWithUnresolvableVariable: 'dataXMLwith${UNRESOLVABLE_VARIABLE}',
      // subdir should be ignored
    });
    // Variable resolvers should only be called for top-level files
    expect(mockVariableResolvers.VARIABLE_1).toHaveBeenCalledTimes(2);
    expect(mockVariableResolvers.VARIABLE_2).toHaveBeenCalledTimes(2);
    // No calls for nested files
    expect(consoleErrorSpy).toHaveBeenCalledTimes(1);
    expect(consoleErrorSpy).toHaveBeenCalledWith(
      'ERROR: Unresolved variable',
      'UNRESOLVABLE_VARIABLE'
    );
  });
});
