const path = require('path');
const fs = require('fs');
const { setupMockFs, clearMockFs } = require('../../__test-utils__/mock-helpers');
const { getMergedResourceFiles, getBaseConfigXML } = require('../get-resources');

jest.mock('fs');

const resource = 'resource';
const proposition = 'proposition';
const territory = 'territory';
const environment = 'environment';
const configPath = path.join(process.cwd(), 'resources/config.xml');

beforeEach(() => {
  clearMockFs(fs);
});

describe('getMergedResourceFiles', () => {
  test('should return root resource if no proposition, territory or environment resource', () => {
    setupMockFs(fs, {
      [path.join(process.cwd(), `resources/${resource}/logo.png`)]: 'root',
    });
    expect(getMergedResourceFiles(resource, proposition, territory, environment)).toStrictEqual({
      'logo.png': 'root',
    });
  });

  test('should return proposition resource if no territory or environment resource', () => {
    setupMockFs(fs, {
      [path.join(process.cwd(), `resources/${resource}/logo.png`)]: 'root',
      [path.join(process.cwd(), `resources/${resource}/${proposition}/logo.png`)]: 'proposition',
    });
    expect(getMergedResourceFiles(resource, proposition, territory, environment)).toStrictEqual({
      'logo.png': 'proposition',
    });
  });

  test('should return territory resource if no environment resource', () => {
    setupMockFs(fs, {
      [path.join(process.cwd(), `resources/${resource}/logo.png`)]: 'root',
      [path.join(process.cwd(), `resources/${resource}/${proposition}/logo.png`)]: 'proposition',
      [path.join(process.cwd(), `resources/${resource}/${proposition}/${territory}/logo.png`)]:
        'territory',
    });
    expect(getMergedResourceFiles(resource, proposition, territory, environment)).toStrictEqual({
      'logo.png': 'territory',
    });
  });

  test('should return environment resource if exists', () => {
    setupMockFs(fs, {
      [path.join(process.cwd(), `resources/${resource}/logo.png`)]: 'root',
      [path.join(process.cwd(), `resources/${resource}/${proposition}/logo.png`)]: 'proposition',
      [path.join(process.cwd(), `resources/${resource}/${proposition}/${territory}/logo.png`)]:
        'territory',
      [path.join(
        process.cwd(),
        `resources/${resource}/${proposition}/${territory}/${environment}/logo.png`
      )]: 'environment',
    });
    expect(getMergedResourceFiles(resource, proposition, territory, environment)).toStrictEqual({
      'logo.png': 'environment',
    });
  });
});

describe('getMergedResourceFiles recursively', () => {
  test('should return nested object for subdirectories when recursive is true', () => {
    setupMockFs(fs, {
      [path.join(process.cwd(), `resources/${resource}/file1.txt`)]: 'root-file',
      [path.join(process.cwd(), `resources/${resource}/subdir/nested.txt`)]: 'nested-file',
      // This will overwrite the root subdir file
      [path.join(process.cwd(), `resources/${resource}/${proposition}/subdir/nested.txt`)]:
        'prop-nested-file',
    });

    const result = getMergedResourceFiles(resource, proposition, territory, environment, true);

    expect(result).toStrictEqual({
      'file1.txt': 'root-file',
      subdir: {
        'nested.txt': 'prop-nested-file',
      },
    });
  });

  test('should ignore subdirectories when recursive is false', () => {
    setupMockFs(fs, {
      [path.join(process.cwd(), `resources/${resource}/file1.txt`)]: 'root-file',
      [path.join(process.cwd(), `resources/${resource}/subdir/nested.txt`)]: 'nested-file',
    });

    const result = getMergedResourceFiles(resource, proposition, territory, environment, false);

    expect(result).toStrictEqual({ 'file1.txt': 'root-file' });
  });
});

describe('getBaseConfigXML', () => {
  test('should read the config xml', () => {
    const mockXmlContent = 'configXMLwith$VARIABLE';
    setupMockFs(fs, {
      [configPath]: mockXmlContent,
    });
    const result = getBaseConfigXML(proposition, territory, environment);
    expect(fs.readFileSync).toHaveBeenCalledTimes(1);
    expect(fs.readFileSync).toHaveBeenCalledWith(configPath, 'utf-8');
    expect(result).toBe(mockXmlContent);
  });
});
