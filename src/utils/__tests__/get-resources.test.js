jest.mock('fs');

const path = require('path');
const fs = require('fs');

const { getMergedResourceFiles, getBaseConfigXML } = require('../get-resources');

const resource = 'resource';
const proposition = 'proposition';
const territory = 'territory';
const environment = 'environment';
const configPath = path.join(process.cwd(), 'resources/config.xml');

describe('getMergedResourceFiles', () => {
  test('should return root resource if no proposition, territory or environment resource', () => {
    const MOCK_FILES = {
      [path.join(process.cwd(), `resources/${resource}/logo.png`)]: 'root',
    };
    require('fs').__setMockFiles(MOCK_FILES);
    expect(getMergedResourceFiles(resource, proposition, territory, environment)).toEqual({
      'logo.png': 'root',
    });
  });

  test('should return proposition resource if no territory or environment resource', () => {
    const MOCK_FILES = {
      [path.join(process.cwd(), `resources/${resource}/logo.png`)]: 'root',
      [path.join(process.cwd(), `resources/${resource}/${proposition}/logo.png`)]: 'proposition',
    };
    require('fs').__setMockFiles(MOCK_FILES);
    expect(getMergedResourceFiles(resource, proposition, territory, environment)).toEqual({
      'logo.png': 'proposition',
    });
  });

  test('should return territory resource if no environment resource', () => {
    const MOCK_FILES = {
      [path.join(process.cwd(), `resources/${resource}/logo.png`)]: 'root',
      [path.join(process.cwd(), `resources/${resource}/${proposition}/logo.png`)]: 'proposition',
      [path.join(process.cwd(), `resources/${resource}/${proposition}/${territory}/logo.png`)]: 'territory',
    };
    require('fs').__setMockFiles(MOCK_FILES);
    expect(getMergedResourceFiles(resource, proposition, territory, environment)).toEqual({
      'logo.png': 'territory',
    });
  });

  test('should return environment resource if exists', () => {
    const MOCK_FILES = {
      [path.join(process.cwd(), `resources/${resource}/logo.png`)]: 'root',
      [path.join(process.cwd(), `resources/${resource}/${proposition}/logo.png`)]: 'proposition',
      [path.join(process.cwd(), `resources/${resource}/${proposition}/${territory}/logo.png`)]: 'territory',
      [path.join(process.cwd(), `resources/${resource}/${proposition}/${territory}/${environment}/logo.png`)]:
        'environment',
    };
    require('fs').__setMockFiles(MOCK_FILES);
    expect(getMergedResourceFiles(resource, proposition, territory, environment)).toEqual({
      'logo.png': 'environment',
    });
  });
});

describe('getBaseConfigXML', () => {
  test('should read the config xml', async () => {
    fs.__setMockFiles({
      [configPath]: 'configXMLwith$VARIABLE',
    });
    await getBaseConfigXML(proposition, territory, environment);
    expect(fs.readFileSync).toHaveBeenCalledTimes(1);
    expect(fs.readFileSync).toHaveBeenCalledWith(configPath, 'utf-8');
  });
});
