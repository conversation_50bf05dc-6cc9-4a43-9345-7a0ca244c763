const { mock, fn } = jest;

mock('fs', () => ({ writeFile: fn((filePath, file, cb) => cb()), mkdir: fn((dir, cb) => cb()) }));

const fs = require('fs');

const { outputData } = require('../output-data');

test('should output file if data is string', async () => {
  const file = './test.txt';
  const data = 'test-data';

  await outputData(file, data);
  expect(fs.writeFile).toHaveBeenCalledTimes(1);
  expect(fs.writeFile).toHaveBeenCalledWith(file, data, expect.any(Function));
});

test('should create dir and output each file if object', async () => {
  const file = 'test';
  const data = { file1: './test.txt', file2: './test2.txt' };

  await outputData(file, data);
  expect(fs.mkdir).toHaveBeenCalledTimes(1);
  expect(fs.mkdir).toHaveBeenCalledWith(file, expect.any(Function));
});

test('should output each file if object', async () => {
  const file = 'test';
  const data = { file1: './test.txt', file2: './test2.txt' };

  await outputData(file, data);
  expect(fs.writeFile).toHaveBeenCalledTimes(2);
  expect(fs.writeFile).toHaveBeenCalledWith('test/file1', data.file1, expect.any(Function));
  expect(fs.writeFile).toHaveBeenCalledWith('test/file2', data.file2, expect.any(Function));
});

test('should create folder for nested folders', async () => {
  const file = 'test';
  const data = { folder: { file1: './test.txt' }, file2: './test2.txt' };

  await outputData(file, data);
  expect(fs.mkdir).toHaveBeenCalledTimes(2);
  expect(fs.mkdir).toHaveBeenCalledWith('test/folder', expect.any(Function));
});

test('should output files in nested folders', async () => {
  const file = 'test';
  const data = { folder: { file1: './test.txt' } };

  await outputData(file, data);
  expect(fs.writeFile).toHaveBeenCalledTimes(1);
  expect(fs.writeFile).toHaveBeenCalledWith(
    'test/folder/file1',
    data.folder.file1,
    expect.any(Function)
  );
});
