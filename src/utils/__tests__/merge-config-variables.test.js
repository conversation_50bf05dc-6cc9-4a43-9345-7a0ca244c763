const path = require('path');

const { mergeConfigVariables } = require('../merge-config-variables');

const proposition = 'proposition';
const territory = 'territory';
const environment = 'environment';

const mockPropositionBuildVariables = {
  variable: 'propositionValue',
  environments: { [environment]: { url: 'url.com' } },
};
const mockTerritoryBuildVariables = {
  variable: 'territoryValue',
};
const mockUpdatedTerritoryBuildVariables = {
  variable: 'updatedTerritoryValue',
  environments: { [environment]: { url: 'newurl.com' } },
};

const propositionPath = path.join(
  process.cwd(),
  `build-variables/proposition/build-variables.json`
);
const territoryPath = path.join(
  process.cwd(),
  `build-variables/proposition/territory/build-variables.json`
);

function setupMockBuildVariables({ propositionMock, territoryMock }) {
  if (propositionMock) {
    jest.doMock(propositionPath, () => propositionMock, { virtual: true });
  }
  if (territoryMock) {
    jest.doMock(territoryPath, () => territoryMock, { virtual: true });
  }
}

beforeEach(() => {
  jest.resetModules();

  // Reset to default empty mocks to ensure clean state
  setupMockBuildVariables({
    propositionMock: {},
    territoryMock: {},
  });
});

test('should return proposition resource if no territory or environment variable', () => {
  setupMockBuildVariables({
    propositionMock: mockPropositionBuildVariables,
  });

  expect(mergeConfigVariables(proposition, territory, environment)).toStrictEqual({
    variable: 'propositionValue',
    environments: { [environment]: { url: 'url.com' } },
    url: 'url.com',
  });
});

test('should return territory resource', () => {
  setupMockBuildVariables({
    propositionMock: mockPropositionBuildVariables,
    territoryMock: mockTerritoryBuildVariables,
  });

  expect(mergeConfigVariables(proposition, territory, environment)).toStrictEqual({
    variable: 'territoryValue',
    environments: { [environment]: { url: 'url.com' } },
    url: 'url.com',
  });
});

test('should return overridden territory environment object if exists', () => {
  setupMockBuildVariables({
    propositionMock: mockPropositionBuildVariables,
    territoryMock: mockUpdatedTerritoryBuildVariables,
  });

  expect(mergeConfigVariables(proposition, territory, environment)).toStrictEqual({
    variable: 'updatedTerritoryValue',
    environments: { [environment]: { url: 'newurl.com' } },
    url: 'newurl.com',
  });
});
