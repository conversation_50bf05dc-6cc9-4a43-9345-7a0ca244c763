const { mock } = jest;
const path = require('path');

const { mergeConfigVariables } = require('../merge-config-variables');

const proposition = 'proposition';
const territory = 'territory';
const environment = 'environment';

const mockPropositionBuildVariables = {
  variable: 'propositionValue',
  environments: { [environment]: { url: 'url.com' } },
};
const mockTerritoryBuildVariables = {
  variable: 'territoryValue',
};
const mockUpdatedTerritoryBuildVariables = {
  variable: 'updatedTerritoryValue',
  environments: { [environment]: { url: 'newurl.com' } },
};

mock(path.join(process.cwd(), `build-variables/proposition/build-variables.json`), () => {}, {
  virtual: true,
});
mock(path.join(process.cwd(), `build-variables/proposition/territory/build-variables.json`), () => {}, {
  virtual: true,
});

beforeEach(() => {
  jest.resetModules();
});

test('should return proposition resource if no territory or environment variable', () => {
  mock(
    path.join(process.cwd(), `build-variables/proposition/build-variables.json`),
    () => mockPropositionBuildVariables,
    { virtual: true }
  );

  expect(mergeConfigVariables(proposition, territory, environment)).toEqual({
    variable: 'propositionValue',
    environments: { [environment]: { url: 'url.com' } },
    url: 'url.com',
  });
});

test('should return territory resource', () => {
  mock(
    path.join(process.cwd(), `build-variables/proposition/build-variables.json`),
    () => mockPropositionBuildVariables,
    { virtual: true }
  );
  mock(
    path.join(process.cwd(), `build-variables/proposition/territory/build-variables.json`),
    () => mockTerritoryBuildVariables,
    {
      virtual: true,
    }
  );

  expect(mergeConfigVariables(proposition, territory, environment)).toEqual({
    variable: 'territoryValue',
    environments: { [environment]: { url: 'url.com' } },
    url: 'url.com',
  });
});

test('should return overridden territory environment object if exists', () => {
  mock(
    path.join(process.cwd(), `build-variables/proposition/build-variables.json`),
    () => mockPropositionBuildVariables,
    { virtual: true }
  );
  mock(
    path.join(process.cwd(), `build-variables/proposition/territory/build-variables.json`),
    () => mockUpdatedTerritoryBuildVariables,
    {
      virtual: true,
    }
  );

  expect(mergeConfigVariables(proposition, territory, environment)).toEqual({
    variable: 'updatedTerritoryValue',
    environments: { [environment]: { url: 'newurl.com' } },
    url: 'newurl.com',
  });
});
