const { mock } = jest;
const path = require('path');

const { mergeConfigVariables } = require('../merge-config-variables');

const proposition = 'proposition';
const territory = 'territory';
const environment = 'environment';

const mockPropositionBuildVariables = {
  variable: 'propositionValue',
  environments: { [environment]: { url: 'url.com' } },
};
const mockTerritoryBuildVariables = {
  variable: 'territoryValue',
};
const mockUpdatedTerritoryBuildVariables = {
  variable: 'updatedTerritoryValue',
  environments: { [environment]: { url: 'newurl.com' } },
};

// Set up default virtual mocks
const propositionPath = path.join(
  process.cwd(),
  `build-variables/proposition/build-variables.json`
);
const territoryPath = path.join(
  process.cwd(),
  `build-variables/proposition/territory/build-variables.json`
);

beforeEach(() => {
  jest.resetModules();
  jest.clearAllMocks();

  // Reset to default empty mocks to ensure clean state
  mock(propositionPath, () => ({}), { virtual: true });
  mock(territoryPath, () => ({}), { virtual: true });
});

test('should return proposition resource if no territory or environment variable', () => {
  mock(propositionPath, () => mockPropositionBuildVariables, { virtual: true });

  expect(mergeConfigVariables(proposition, territory, environment)).toEqual({
    variable: 'propositionValue',
    environments: { [environment]: { url: 'url.com' } },
    url: 'url.com',
  });
});

test('should return territory resource', () => {
  mock(propositionPath, () => mockPropositionBuildVariables, { virtual: true });
  mock(territoryPath, () => mockTerritoryBuildVariables, { virtual: true });

  expect(mergeConfigVariables(proposition, territory, environment)).toEqual({
    variable: 'territoryValue',
    environments: { [environment]: { url: 'url.com' } },
    url: 'url.com',
  });
});

test('should return overridden territory environment object if exists', () => {
  mock(
    path.join(process.cwd(), `build-variables/proposition/build-variables.json`),
    () => mockPropositionBuildVariables,
    { virtual: true }
  );
  mock(
    path.join(process.cwd(), `build-variables/proposition/territory/build-variables.json`),
    () => mockUpdatedTerritoryBuildVariables,
    {
      virtual: true,
    }
  );

  expect(mergeConfigVariables(proposition, territory, environment)).toEqual({
    variable: 'updatedTerritoryValue',
    environments: { [environment]: { url: 'newurl.com' } },
    url: 'newurl.com',
  });
});
