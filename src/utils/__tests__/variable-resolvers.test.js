const mockConfig = {
  url: 'url/',
  query: 'query',
  territory: 'territory',
  applicationId: 'packageId.applicationName',
  version: '9.99',
};

const variableResolver = require('../variable-resolvers');

test('should return built url', () => {
  expect(variableResolver.url(mockConfig)).toBe(
    'url/?query&container=tizen&container_version=9.99'
  );
});

test('should return built url appending container query params', () => {
  expect(variableResolver.url({ ...mockConfig })).toBe(
    'url/?query&container=tizen&container_version=9.99'
  );
});

test.each(['NOW', 'WOW'])('should support feature flags on url for %s', (proposition) => {
  expect(
    variableResolver.url({
      ...mockConfig,
      name: proposition,
      featureflags: 'flag1, flag2',
    })
  ).toBe(
    'url/?query&FLAG__flag1=enabled&FLAG__flag2=enabled&container=tizen&container_version=9.99'
  );
});

test('should support feature flags on url for other providers', () => {
  expect(
    variableResolver.url({
      ...mockConfig,
      name: 'notnow',
      featureflags: 'flag1, flag2',
    })
  ).toBe(
    'url/?query&FLAG__flag1=enabled&FLAG__flag2=enabled&container=tizen&container_version=9.99'
  );
});

test('should return escaped url', () => {
  expect(variableResolver.escapedUrl(mockConfig)).toBe(
    'url/?query&amp;container=tizen&amp;container_version=9.99'
  );
});

test('should return applicationName', () => {
  expect(variableResolver.applicationName(mockConfig)).toBe('applicationName');
});

test('should return packageId', () => {
  expect(variableResolver.packageId(mockConfig)).toBe('packageId');
});
