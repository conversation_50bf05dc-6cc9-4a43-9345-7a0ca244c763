const fs = require('fs');
const path = require('path');

const { applyVariables } = require('./utils/apply-variables');
const { mergeConfigVariables } = require('./utils/merge-config-variables');
const { outputData } = require('./utils/output-data');
const { getMergedResourceFiles, getBaseConfigXML } = require('./utils/get-resources');
const { url } = require('./utils/variable-resolvers');

const outputDir = path.join(process.cwd(), 'dist');

async function output(data) {
  fs.rmSync(outputDir, { recursive: true, force: true });

  return outputData(outputDir, data);
}

function applyVariablesHTML(config, proposition, territory, environment, image) {
  // Extracting the html config to flatten and combine with the main config;
  // Extracting the css config to discard as it isn't necessary in this step;
  const { html, css, ...defaultConfig } = config;
  const file = getMergedResourceFiles('html', proposition, territory, environment);
  const splashScreen = `&quot;data:image/png;base64,${image}&quot;`;

  return applyVariables({ ...defaultConfig, ...html, splashScreen }, file, { isFolder: true });
}

async function build({ proposition, territory, environment, featureflags, customURL, device }) {
  const configVariables = await mergeConfigVariables(proposition, territory, environment, device);
  const clientSecret = Buffer.from(`${configVariables.clientSecret}`, 'base64').toString('utf-8');
  const clientToken = Buffer.from(`${configVariables.clientId}:${clientSecret}`).toString('base64');
  const config = { ...configVariables, featureflags, customURL, clientToken };

  const images = getMergedResourceFiles('images', proposition, territory, environment);
  const splashScreen = Buffer.from(images[`splashScreen.png`]).toString('base64');

  // TODO: https://gspcloud.atlassian.net/browse/LTV-20581
  // The splashScreen.png file is only used to create the base64 and map it to the HTML file,
  // it's not needed for the final build
  delete images[`splashScreen.png`];

  await output({
    'config.xml': applyVariables(config, getBaseConfigXML()),
    images,
    css: applyVariables(config.css, getMergedResourceFiles('css', proposition, territory, environment), {
      isFolder: true,
    }),
    fonts: getMergedResourceFiles('fonts', proposition, territory, environment),
    html: applyVariablesHTML(config, proposition, territory, environment, splashScreen),
    js: applyVariables(config, getMergedResourceFiles('js', proposition, territory, environment), {
      isFolder: true,
    }),
    utils: applyVariables(config, getMergedResourceFiles('utils', proposition, territory, environment), {
      isFolder: true,
    }),
    ...getMergedResourceFiles('project', proposition, territory, environment),
  });

  console.log('Built:', url(config)); // can see if the correct url was built from the console
}

module.exports = { build };
