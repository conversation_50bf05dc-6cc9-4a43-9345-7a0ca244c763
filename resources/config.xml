<?xml version="1.0" encoding="UTF-8"?>
<widget xmlns:tizen="http://tizen.org/ns/widgets" xmlns="http://www.w3.org/ns/widgets" id="${authorUrl}${applicationName}" version="${version}" height="1080" width="1920" viewmodes="maximized">
    <access origin="http://nowtv.com" subdomains="true"></access>
    <access origin="https://nowtv.com" subdomains="true"></access>
    <access origin="http://bskyb.com" subdomains="true"></access>
    <access origin="https://bskyb.com" subdomains="true"></access>
    <access origin="http://conviva.com" subdomains="true"></access>
    <access origin="https://conviva.com" subdomains="true"></access>
    <access origin="http://sky.com" subdomains="true"></access>
    <access origin="https://sky.com" subdomains="true"></access>
    <access origin="http://amazonaws.com" subdomains="true"></access>
    <access origin="https://amazonaws.com" subdomains="true"></access>
    <access origin="http://demdex.com" subdomains="true"></access>
    <access origin="https://demdex.com" subdomains="true"></access>
    <access origin="http://epgsky.com" subdomains="true"></access>
    <access origin="https://epgsky.com" subdomains="true"></access>
    <access origin="https://peacocktv.com" subdomains="true"></access>
    <access origin="https://skyshowtime.com" subdomains="true"></access>
    <access origin="https://ott.showmax.com" subdomains="true"></access>
    <access origin="*" subdomains="true"></access>
    <tizen:application id="${applicationId}" package="${packageId}" required_version="${appRequiredVersion}"/>
    <author href="${authorUrl}">${author}</author>
    <tizen:app-control>
        <tizen:src name="${escapedUrl}" reload="disable"/>
        <tizen:operation name="http://samsung.com/appcontrol/operation/eden_resume"/>
    </tizen:app-control>
    <content src="html/index.html"/>
    <feature name="http://tizen.org/feature/screen.size.normal.1080.1920"/>
    <feature name="http://tizen.org/feature/screen.size.all"/>
    <feature name="http://tizen.org/feature/tv.inputdevice"/>
    <tizen:metadata key="http://samsung.com/tv/metadata/search.format" value="%%CONTENT_ID%%"/>
    <tizen:metadata key="http://samsung.com/tv/metadata/prelaunch.support" value="true"/>
    <tizen:metadata key="http://samsung.com/tv/metadata/devel.api.version" value="${developerApiVersion}"/>
    <tizen:metadata key="http://samsung.com/tv/metadata/multitasking.support" value="true"/>
    <name>${name}</name>
    <icon src="images/icon.png"/>
    <tizen:privilege name="http://tizen.org/privilege/application.launch"/>
    <tizen:privilege name="http://tizen.org/privilege/application.info"/>
    <tizen:privilege name="http://tizen.org/privilege/package.info"/>
    <tizen:privilege name="http://tizen.org/privilege/system"/>
    <tizen:privilege name="http://developer.samsung.com/privilege/hostedapp_deviceapi_allow"/>
    <tizen:privilege name="http://developer.samsung.com/privilege/avplay"/>
    <tizen:privilege name="http://developer.samsung.com/privilege/drmplay"/>
    <tizen:privilege name="http://tizen.org/privilege/filesystem.write"/>
    <tizen:privilege name="http://tizen.org/privilege/filesystem.read"/>
    <tizen:privilege name="http://tizen.org/privilege/download"/>
    <tizen:privilege name="http://tizen.org/privilege/tv.inputdevice"/>
    <tizen:privilege name="http://developer.samsung.com/privilege/drminfo"/>
    <tizen:privilege name="http://developer.samsung.com/privilege/network.public"/>
    <tizen:privilege name="http://developer.samsung.com/privilege/productinfo"/>
    ${adInfoPrivilege}
    ${secureStoragePrivilege}
    ${voicePrivilege}
    ${edenPreviewBackgroundService}
    ${cwBackgroundService}
    ${voiceOnSearch}
    <tizen:profile name="tv"/>
    <tizen:setting pointing-device-support='disable' />
    <tizen:setting screen-orientation="landscape" context-menu="enable" background-support="disable" encryption="disable" install-location="auto" hwkey-event="${hardwareKeyEvent}"/>
    <tizen:setting/>
</widget>
