var http = require('http');
var path = require('path');
var familyValue = 4;

function _detectProtocol() {
  // making a getItems request to identify which familyValue is to be used in follow up calls
  // familyValue = 4 (IPv4) -> 18/19 TV
  // familyValue = 6 (IPv6) -> 20 TV ~
  return new Promise(function (resolve) {
    var options = {
      hostname: 'localhost',
      port: 9013,
      path: '/?module=EBHelper&func=GetItems',
      method: 'GET',
      family: familyValue,
      headers: {
        app_id: '${storeId}',
      },
    };

    var req = http.request(options, function (res) {
      res.on('data', function () {
        resolve();
      });
    });

    req.on('error', function (error) {
      if (error.code === 'ECONNREFUSED') {
        familyValue = 6;
      }
      resolve();
    });

    req.end();
  });
}

function _getItems() {
  return new Promise(function (resolve, reject) {
    var options = {
      hostname: 'localhost',
      port: '9013',
      path: '/?module=EBHelper&func=GetItems',
      method: 'GET',
      family: familyValue,
      headers: {
        app_id: '${storeId}',
      },
    };

    var req = http.request(options, function (res) {
      var data = '';
      res.on('data', function (chunk) {
        data += chunk.toString();
      });

      res.on('end', function () {
        try {
          var response = JSON.parse(data);
          resolve((response && (response.rsp || response)) || []); // response is different on older models
        } catch (error) {
          reject(new Error('Unable to parse response JSON -> ' + error.message));
        }
      });
    });

    req.on('error', function (error) {
      reject(new Error('CW get items error -> ' + error.message));
    });

    req.end();
  });
}

function _getAppIconPath(applicationId) {
  var affixes = applicationId.split('.');

  return '/opt/usr/apps/' + affixes[0] + '/shared/res/${applicationId}.png';
}

function _processAssetImageURL(imageURL) {
  if (!imageURL) {
    return ''; // if no/invalid url, send nothing, cw will skip
  }

  try {
    var newURL = new URL(imageURL);
    newURL.pathname = path.join(newURL.pathname, '400x224');
    return newURL.toString();
  } catch (_) {
    // Samsung 2018 and previous do not support URL, falling back to replace
    return imageURL.replace('_16_9?', '_16_9/400x224?');
  }
}

// needed to clean the payload that we get from CLIP
function _processPayload(payload) {
  if (payload) {
    var deeplinkData = payload.match(/%7B.*?%7D/g);
    if (deeplinkData && deeplinkData[0]) {
      return 'deeplinkData=' + deeplinkData[0];
    }
  }
  return ''; // this should not happen
}

function makeStringSafe(str) {
  // eslint-disable-next-line no-useless-escape
  return str ? str.replace(/[^\w\s.,\-:;'"@#/\!?&%$€£]/gi, '') : '';
}

function _addItem(assetInfo) {
  return new Promise(function (resolve, reject) {
    try {
      var options = {
        hostname: 'localhost',
        port: '9013',
        path: '/?module=EBHelper&func=AddItem',
        method: 'GET',
        family: familyValue,
        headers: {
          field: 0,
          app_id: '${storeId}',
          app_name: '${name}',
          app_icon: _getAppIconPath('${applicationId}'),
          content_id: assetInfo.contentId || '',
          payload: _processPayload(assetInfo.payload),
          image_url: _processAssetImageURL(assetInfo.imageUrl),
          content_title: makeStringSafe(assetInfo.contentTitle),
          sub_title: makeStringSafe(assetInfo.subTitle),
          description: makeStringSafe(assetInfo.description),
          rate: assetInfo.rate || '',
          genre: makeStringSafe(assetInfo.genre),
          release: assetInfo.release || new Date().getFullYear(),
          duration: assetInfo.duration || 0,
          playback: assetInfo.playback || 0,
          expiry: assetInfo.expiry || 0,
          timestamp: assetInfo.timestamp || 0,
        },
      };

      var req = http.request(options, function (res) {
        res.on('end', function () {
          resolve();
        });
      });

      req.on('error', function (error) {
        reject(
          new Error(
            'CW add item request error -> '.concat(
              'pvid: ',
              assetInfo.contentId,
              ' | title: ',
              assetInfo.contentTitle,
              ' | error: ',
              error.message
            )
          )
        );
      });

      req.end();
    } catch (error) {
      reject(
        new Error(
          'CW add item catch error -> '.concat(
            'pvid: ',
            assetInfo.contentId,
            ' | title: ',
            assetInfo.contentTitle,
            ' | error: ',
            error.message
          )
        )
      );
    }
  });
}

function _removeItem(contentId) {
  return new Promise(function (resolve, reject) {
    var options = {
      hostname: 'localhost',
      port: '9013',
      path: '/?module=EBHelper&func=DeleteItem',
      method: 'GET',
      family: familyValue,
      headers: {
        field: 0,
        app_id: '${storeId}',
        content_id: contentId,
      },
    };

    var req = http.request(options, function (res) {
      res.on('end', function () {
        resolve();
      });
    });

    req.on('error', function (error) {
      reject(new Error('CW remove item error -> ' + error.message));
    });

    req.end();
  });
}

function getCWItems() {
  return _detectProtocol().then(_getItems);
}

function addCWItems(assets) {
  var addItemsQueue = [];

  return removeCWItems().then(function () {
    for (var j = 0; assets && j < assets.length; j++) {
      addItemsQueue.push(_addItem(assets[j]));
    }

    return Promise.all(addItemsQueue);
  });
}

function removeCWItems() {
  var removeItemsQueue = [];

  return getCWItems().then(function (assetsOnCW) {
    for (var i = 0; assetsOnCW && i < assetsOnCW.length; i++) {
      removeItemsQueue.push(_removeItem(assetsOnCW[i].content_id));
    }

    return Promise.all(removeItemsQueue);
  });
}

module.exports = { addCWItems: addCWItems, removeCWItems: removeCWItems };
