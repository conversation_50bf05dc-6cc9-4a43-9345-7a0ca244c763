// To log data to a local server
// var log = require('../utils/logger_local.js');
// log.logMessage('step','message');
// should be kept compatible with newrelic logger for easier switching

var http = require('http');

var uuid = require('./uuid.js');
var sessionID = uuid.generateUUID();

function logMessage(step, message, appData, error) {
  var processedData = JSON.stringify({
    step: step,
    message: message,
    appData: appData,
    errorMessage: error,
    containerSession: sessionID,
  });

  var options = {
    hostname: '************', // Should be dev ip
    port: 4500,
    path: '/log',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Content-Length': processedData.length,
    },
  };

  var req = http.request(options, function () {}).on('error', function () {});

  req.write(processedData);
  req.end();
}

module.exports = {
  logMessage: logMessage,
  setShouldLog: function () {}, // keeps compatibility with nr logger
  setAction: function () {}, // keeps compatibility with nr logger
  containerSession: sessionID,
};
