// To log data to newrelic
// var log = require('../utils/logger_newrelic.js');
// log.logMessage('step','message');

var https = require('https');
var uuid = require('./uuid.js');

var shouldLog = false;
var action = 'samsung-container';
var sessionID = uuid.generateUUID();
var revokeAccess,
  hasUserToken,
  hasPersonaID = false;

var capabilities = {
  platformVersion: tizen.systeminfo.getCapability('http://tizen.org/feature/platform.version'),
  nativeApiVersion: tizen.systeminfo.getCapability(
    'http://tizen.org/feature/platform.native.api.version'
  ),
  webApiVersion: tizen.systeminfo.getCapability(
    'http://tizen.org/feature/platform.web.api.version'
  ),
};

function _getBaseLog() {
  return {
    platform: 'SAMSUNG',
    proposition: 'NBCUOTT',
    provider: 'NBCU',
    containerVersion: '${version}',
    territory: 'US',
    osName: 'Tizen',
    osVersion:
      'Platform: ' +
      capabilities.platformVersion +
      ' | API: ' +
      capabilities.nativeApiVersion +
      ' | Web API: ' +
      capabilities.webApiVersion,
    containerSession: sessionID,
    level: 'info',
    logtype: 'container',
  };
}

function logMessage(step, message, appData, error) {
  if (shouldLog) {
    var log = _getBaseLog();
    log.action = action;
    log.step = step;
    log.message = message;
    if (appData) {
      revokeAccess = appData.revokeAccess;
      hasUserToken = !!appData.userToken;
      hasPersonaID = !!appData.personaId;
    }
    log.revokeAccess = revokeAccess;
    log.hasUserToken = hasUserToken;
    log.hasPersonaID = hasPersonaID;
    if (error) {
      log.level = 'error';
      log.errorMessage = error;
    }

    var processedData = JSON.stringify(log);

    var options = {
      hostname: 'log-api.newrelic.com',
      path: '/log/v1',
      method: 'POST',
      headers: {
        'Api-Key': '${newRelicApiKey}',
        'Content-Type': 'application/json',
        'Content-Length': processedData.length,
      },
    };

    var req = https.request(options, function () {});

    req.on('error', function () {});

    req.write(processedData);
    req.end();
  }
}

function setShouldLog(newShouldLog) {
  shouldLog = newShouldLog;
}

function setAction(newAction) {
  action = newAction;
}

module.exports = {
  logMessage: logMessage,
  setShouldLog: setShouldLog,
  setAction: setAction,
  containerSession: sessionID,
};
