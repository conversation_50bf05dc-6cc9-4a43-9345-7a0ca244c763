function getFile(filePath) {
  if (!filePath) {
    return Promise.reject('Empty file path.');
  }

  return new Promise(function (resolve, reject) {
    tizen.filesystem.resolve(
      'documents',
      function (documentsDir) {
        documentsDir.listFiles(
          function (files) {
            for (var i = 0; i < files.length; i++) {
              if (files[i].name === filePath) {
                return resolve({
                  dir: documentsDir,
                  file: files[i],
                });
              }
            }

            var file = documentsDir.createFile(filePath);

            resolve({
              dir: documentsDir,
              file: file,
            });
          },
          function (error) {
            reject(new Error('Cannot find a file.', { cause: error }));
          }
        );
      },
      function (error) {
        reject(new Error('File does not exists.', { cause: error }));
      }
    );
  });
}

function deleteFile(filePath) {
  if (!filePath) {
    return Promise.reject('Empty file path.');
  }

  return new Promise(function (resolve, reject) {
    tizen.filesystem.resolve(
      'documents',
      function (documentsDir) {
        documentsDir.listFiles(
          function (files) {
            for (var i = 0; i < files.length; i++) {
              if (files[i].name === filePath) {
                documentsDir.deleteFile(files[i].fullPath);
              }
            }
            resolve();
          },
          function (error) {
            reject(new Error('Cannot find a file.', { cause: error }));
          }
        );
      },
      function (error) {
        reject(new Error('File does not exist.', { cause: error }));
      }
    );
  });
}

function replaceFile(filePath, data) {
  return new Promise(function (resolve, reject) {
    getFile(filePath).then(
      // eslint-disable-next-line promise/always-return
      function (file) {
        file.dir.deleteFile(
          file.file.fullPath,
          function () {
            var newFile = file.dir.createFile(filePath);
            newFile.openStream(
              'w',
              function (fs) {
                fs.write(data);
                fs.close();
                resolve();
              },
              function () {
                reject(new Error('Cannot write file.'));
              },
              'UTF-8'
            );
          },
          function () {
            reject(new Error('Cannot delete file.'));
          }
        );
      },
      function (error) {
        reject(new Error('Error getting file: ' + error.message));
      }
    );
  });
}

module.exports = { replaceFile: replaceFile, getFile: getFile, deleteFile: deleteFile };
