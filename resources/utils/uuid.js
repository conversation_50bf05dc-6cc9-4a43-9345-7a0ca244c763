function generateUUID() {
  var uuid; // generated uuid
  var dt = new Date().getTime(); // current date

  try {
    uuid = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
      var r = (dt + Math.random() * 16) % 16 | 0;
      dt = Math.floor(dt / 16);
      return (c === 'x' ? r : (r & 0x3) | 0x8).toString(16);
    });
  } catch (_) {
    uuid = dt.toString();
  }

  return uuid;
}

module.exports = { generateUUID: generateUUID };
