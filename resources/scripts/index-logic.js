(function () {
  var config;

  var delta =
    window.performance && window.performance.now ? Math.round(window.performance.now()) : 0;
  var containerStartTime = Date.now() - delta;

  var attemptCounter;
  var containerSession;
  var queryParams;
  var utils;
  var LOG_ID = {};

  function defineVars() {
    utils = window.utils;
    LOG_ID.ATTEMPT = utils.logger.createId();
    queryParams = utils.url.getQueryParams();
    containerSession = queryParams.containerSession || utils.generateUUID();
    attemptCounter = Number(queryParams.attemptCounter) || 0;
  }

  function initialiseEventListeners() {
    document.body.addEventListener('keydown', function (e) {
      if (e.keyCode === 10009) {
        // back
        try {
          window.tizen.application.getCurrentApplication().exit();
        } catch (_) {
          // noop
        }
      }
    });
  }

  function initialiseLogger() {
    utils.logger.addBaseData({
      containerSession: containerSession,
      level: utils.logger.LOG_LEVEL.INFO,
      requestType: utils.request.getRequestType(),
    });
  }

  function goToErrorPage(errorParams) {
    window.location.href = utils.url.append('error.html', {
      appUrl: errorParams.appUrl,
      attemptCounter: attemptCounter,
      containerSession: containerSession,
      containerStartTime: containerStartTime,
    });
  }

  function retryOrError(errorParams) {
    var remainder = attemptCounter % config.maxAttempts;
    if (remainder === 0) {
      // Small delay to allow the last NR request to hopefully hit the server before the location change,
      // or it might be cancelled by the browser and not logged in.
      window.setTimeout(function () {
        goToErrorPage(errorParams);
      }, 1000);
    } else {
      var delay = config.linearDelayBetweenAttempts
        ? config.delayBetweenAttempts * remainder
        : config.delayBetweenAttempts;
      window.setTimeout(validateAppUrl, delay);
    }
  }

  function validateAppUrl() {
    attemptCounter++;

    var htmlLoadStartTime = Date.now();
    var url = utils.url.append(config.appUrl, {
      containerStartTime: containerStartTime,
      htmlLoadStartTime: htmlLoadStartTime,
    });

    utils.logger.append(LOG_ID.ATTEMPT, {
      appUrl: url,
      step: 'attempt ' + attemptCounter,
    });

    function ok(response) {
      delete response.responseData;
      utils.logger.append(LOG_ID.ATTEMPT, response);
      utils.logger.send(LOG_ID.ATTEMPT);
      window.location.href = url;
    }

    function ko(response) {
      utils.logger.append(LOG_ID.ATTEMPT, response);
      utils.logger.send(LOG_ID.ATTEMPT);
      retryOrError({ appUrl: url });
    }

    utils.request({
      url: url,
      timeout: config.requestTimeout,
      method: 'GET',
      fail: ko,
      success: ok,
    });
  }

  /**
   * Initializes the application logic with the given configuration.
   * @param {object} baseConfig - The configuration object.
   * @param {string} baseConfig.appUrl - The URL of the main application to load.
   * @param {number} baseConfig.maxAttempts - The maximum number of times to try loading the application.
   * @param {number} baseConfig.delayBetweenAttempts - The base delay in milliseconds between retry attempts.
   * @param {boolean} [baseConfig.linearDelayBetweenAttempts] - If true, the delay between retries increases linearly. If false or undefined, a fixed delay is used.
   * @param {number} baseConfig.requestTimeout - The timeout in milliseconds for the application load request.
   */
  function init(baseConfig) {
    config = baseConfig;
    defineVars();
    initialiseEventListeners();
    initialiseLogger();
    validateAppUrl();
  }

  window.indexLogic = {
    init: init,
  };
})();
