/**
 * A simple Promise polyfill implementation compatible with ES5 environments.
 *
 * This polyfill is **specifically needed for Samsung 2016 Smart TVs (Tizen 2.4)**,
 * which do not provide a native Promise implementation.
 *
 * Features:
 * - Constructor with an executor function.
 * - `resolve` and `reject` states.
 * - `then` method for chaining callbacks.
 * - `catch` method for error handling.
 * - Basic support for `Promise.resolve`, `Promise.reject`, `Promise.all`, and `Promise.race`.
 *
 * It safely adds Promise if it is missing, following the ES6 specification.
 * Modern browsers and newer Samsung TVs do not require this polyfill.
 *
 * Limitations:
 * - Does not fully handle microtask queueing like native Promises (uses `setTimeout` for async execution).
 * - Does not handle all edge cases of promise resolution/rejection (e.g., thenables, cyclic promises).
 *
 * This polyfill is sufficient for most practical uses on Tizen 2.4, especially for simple async flows and fetch/XHR wrappers.
 * If you need full spec compliance, consider using a well-tested library like es6-promise
 * https://github.com/stefanpenner/es6-promise
 */

'use strict';

(function () {
  // Check if Promise already exists to prevent overwriting native implementation
  if (typeof window.Promise === 'undefined') {
    var PromisePolyfill = function (executor) {
      var state = 'pending', // Initial state: pending, fulfilled, or rejected
        value, // Result if fulfilled
        reason, // Reason if rejected
        handlers = []; // Array of { onFulfilled, onRejected } callbacks

      function resolve(val) {
        if (state !== 'pending') {
          return; // Only transition from pending
        }

        // Handle "thenables" - objects that have a .then method
        if (val && typeof val.then === 'function') {
          try {
            val.then(resolve, reject);
            return;
          } catch (e) {
            reject(e);
            return;
          }
        }

        state = 'fulfilled';
        value = val;

        // Asynchronously execute all pending handlers
        handlers.forEach(handle);
      }

      function reject(err) {
        if (state !== 'pending') {
          return; // Only transition from pending
        }

        state = 'rejected';
        reason = err;

        // Asynchronously execute all pending handlers
        handlers.forEach(handle);
      }

      function handle(handler) {
        setTimeout(function () {
          if (state === 'fulfilled') {
            try {
              var successRes = handler.onFulfilled ? handler.onFulfilled(value) : value;
              handler.resolve(successRes);
            } catch (error) {
              handler.reject(error);
            }
          } else if (state === 'rejected') {
            try {
              var failRes = handler.onRejected ? handler.onRejected(reason) : reason;
              handler.reject(failRes);
            } catch (error) {
              handler.reject(error);
            }
          }
        }, 0);
      }

      this.then = function (onFulfilled, onRejected) {
        return new PromisePolyfill(function (nextResolve, nextReject) {
          handlers.push({
            onFulfilled: onFulfilled,
            onRejected: onRejected,
            resolve: nextResolve,
            reject: nextReject,
          });

          if (state !== 'pending') {
            handle(handlers[handlers.length - 1]);
          }
        });
      };

      this['catch'] = function (onRejected) {
        return this.then(undefined, onRejected);
      };

      try {
        executor(resolve, reject);
      } catch (error) {
        reject(error);
      }
    };

    PromisePolyfill.resolve = function (value) {
      if (value instanceof PromisePolyfill) {
        return value;
      }
      return new PromisePolyfill(function (resolve) {
        resolve(value);
      });
    };

    PromisePolyfill.reject = function (reason) {
      return new PromisePolyfill(function (resolve, reject) {
        reject(reason);
      });
    };

    var handlePromiseAtIndex = function (arr, i, results, resolve, reject, PromisePolyfill) {
      PromisePolyfill.resolve(arr[i]).then(
        function (val) {
          results[i] = val;
          handlePromiseAtIndex.completed++;
          if (handlePromiseAtIndex.completed === arr.length) {
            resolve(results);
          }
        },
        function (err) {
          reject(err);
        }
      );
    };

    // ES5-compatible Promise.all
    PromisePolyfill.all = function (arr) {
      return new PromisePolyfill(function (resolve, reject) {
        if (!arr || typeof arr.length !== 'number') {
          return reject(new TypeError('Promise.all requires an array'));
        }
        var results = [];
        handlePromiseAtIndex.completed = 0;
        if (arr.length === 0) {
          return resolve([]);
        }
        for (var i = 0; i < arr.length; i++) {
          handlePromiseAtIndex(arr, i, results, resolve, reject, PromisePolyfill);
        }
      });
    };

    // ES5-compatible Promise.race
    PromisePolyfill.race = function (arr) {
      return new PromisePolyfill(function (resolve, reject) {
        if (!arr || typeof arr.length !== 'number') {
          return reject(new TypeError('Promise.race requires an array'));
        }
        for (var i = 0; i < arr.length; i++) {
          PromisePolyfill.resolve(arr[i]).then(resolve, reject);
        }
      });
    };

    window.Promise = PromisePolyfill;
  }
})();
