/**
 * A simple Promise polyfill implementation compatible with ES5 environments.
 *
 * This polyfill is **specifically needed for Samsung 2016 Smart TVs (Tizen 2.4)**,
 * which do not provide a native Promise implementation.
 *
 * Features:
 * - Constructor with an executor function.
 * - `resolve` and `reject` states.
 * - `then` method for chaining callbacks.
 * - `catch` method for error handling.
 * - Basic support for `Promise.resolve`, `Promise.reject`, `Promise.all`, and `Promise.race`.
 * - `finally` is intentionally not implemented, because native support of it only starts from Tizen 5.0 and newer models (Samsung 2019+)
 *
 * Limitations:
 * - Does not fully handle microtask queueing like native Promises (uses `setTimeout` for async execution).
 * - Does not handle all edge cases of promise resolution/rejection (e.g., thenables, cyclic promises).
 *
 * This polyfill is sufficient for most practical uses on Tizen 2.4, especially for simple async flows and fetch/XHR wrappers.
 * If you need full spec compliance, consider using a well-tested library like es6-promise
 * https://github.com/stefanpenner/es6-promise
 */

'use strict';

(function () {
  // Check if Promise already exists to prevent overwriting native implementation
  if (typeof window.Promise === 'undefined') {
    var PromisePolyfill = function (executor) {
      var state = 'pending', // Initial state: pending, fulfilled, or rejected
        value, // Result if fulfilled
        reason, // Reason if rejected
        handlers = []; // Array of { onFulfilled, onRejected } callbacks

      function resolve(val) {
        if (state !== 'pending') {
          return; // Only transition from pending
        }

        // Handle "thenables" - objects that have a .then method
        if (val && typeof val.then === 'function') {
          try {
            // This is a standard way to unwrap a thenable inside a promise polyfill.
            // The `promise/catch-or-return` rule produces a false positive here because
            // the rejection is correctly handled by passing `reject` as the second argument.
            // eslint-disable-next-line promise/catch-or-return
            val.then(resolve, reject);
            return;
          } catch (e) {
            reject(e);
            return;
          }
        }

        state = 'fulfilled';
        value = val;

        // Asynchronously execute all pending handlers
        handlers.forEach(handle);
      }

      function reject(err) {
        if (state !== 'pending') {
          return; // Only transition from pending
        }

        state = 'rejected';
        reason = err;

        // Asynchronously execute all pending handlers
        handlers.forEach(handle);
      }

      function handle(handler) {
        setTimeout(function () {
          if (state === 'fulfilled') {
            try {
              var successRes = handler.onFulfilled ? handler.onFulfilled(value) : value;
              handler.resolve(successRes);
            } catch (error) {
              handler.reject(error);
            }
          } else if (state === 'rejected') {
            // If a rejection handler is provided, its successful execution
            // should resolve the next promise in the chain, allowing for
            // error recovery via .catch() or .then(null, onRejected).
            if (handler.onRejected) {
              try {
                handler.resolve(handler.onRejected(reason));
              } catch (e) {
                handler.reject(e);
              }
            } else {
              // If there's no rejection handler, propagate the rejection.
              handler.reject(reason);
            }
          }
        }, 0);
      }

      this.then = function (onFulfilled, onRejected) {
        return new PromisePolyfill(function (nextResolve, nextReject) {
          handlers.push({
            onFulfilled: onFulfilled,
            onRejected: onRejected,
            resolve: nextResolve,
            reject: nextReject,
          });

          if (state !== 'pending') {
            handle(handlers[handlers.length - 1]);
          }
        });
      };

      this['catch'] = function (onRejected) {
        return this.then(undefined, onRejected);
      };

      try {
        executor(resolve, reject);
      } catch (error) {
        reject(error);
      }
    };

    PromisePolyfill.resolve = function (value) {
      if (value instanceof PromisePolyfill) {
        return value;
      }
      return new PromisePolyfill(function (resolve) {
        resolve(value);
      });
    };

    PromisePolyfill.reject = function (reason) {
      return new PromisePolyfill(function (_, reject) {
        reject(reason);
      });
    };

    var handlePromiseAtIndex = function (arr, i, results, resolve, reject, PromisePolyfill) {
      // The `promise/catch-or-return` rule produces a false positive here.
      // The rejection of an individual promise is correctly handled by passing the
      // `reject` function of the `Promise.all` wrapper as the second argument to `then`.
      // eslint-disable-next-line promise/catch-or-return
      PromisePolyfill.resolve(arr[i]).then(
        function (val) {
          results[i] = val;
          handlePromiseAtIndex.completed++;
          if (handlePromiseAtIndex.completed === arr.length) {
            resolve(results);
          }
          return;
        },
        function (err) {
          reject(err);
        }
      );
    };

    // ES5-compatible Promise.all
    PromisePolyfill.all = function (arr) {
      return new PromisePolyfill(function (resolve, reject) {
        if (!arr || typeof arr.length !== 'number') {
          return reject(new TypeError('Promise.all requires an array'));
        }
        var results = [];
        handlePromiseAtIndex.completed = 0;
        if (arr.length === 0) {
          return resolve([]);
        }
        for (var i = 0; i < arr.length; i++) {
          handlePromiseAtIndex(arr, i, results, resolve, reject, PromisePolyfill);
        }
      });
    };

    // ES5-compatible Promise.race
    PromisePolyfill.race = function (arr) {
      return new PromisePolyfill(function (resolve, reject) {
        if (!arr || typeof arr.length !== 'number') {
          return reject(new TypeError('Promise.race requires an array'));
        }
        for (var i = 0; i < arr.length; i++) {
          // The `promise/catch-or-return` rule produces a false positive here.
          // The rejection of an individual promise is correctly handled by passing the
          // `reject` function of the `Promise.race` wrapper as the second argument to `then`.
          // eslint-disable-next-line promise/catch-or-return
          PromisePolyfill.resolve(arr[i]).then(resolve, reject);
        }
      });
    };

    window.Promise = PromisePolyfill;
  }
})();
