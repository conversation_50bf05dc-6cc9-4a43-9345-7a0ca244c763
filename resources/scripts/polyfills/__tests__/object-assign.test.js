describe('Object.assign Polyfill', () => {
  let ObjectAssignPolyfill;
  let originalObjectAssign;

  beforeEach(() => {
    // Store original Object.assign if it exists
    originalObjectAssign = global.Object.assign;

    // Isolate the module to ensure the polyfill is re-applied for each test
    jest.isolateModules(() => {
      // Set up a mock global object without a native Object.assign
      if (global.Object && global.Object.assign) {
        delete global.Object.assign;
      }
      global.Object = global.Object || {}; // Ensure Object exists

      // Load the polyfill, which will attach itself to Object.assign
      require('../object-assign.js');
      ObjectAssignPolyfill = global.Object.assign;
    });
  });

  afterEach(() => {
    // Clean up and restore original Object.assign
    if (originalObjectAssign) {
      global.Object.assign = originalObjectAssign;
    } else {
      delete global.Object.assign;
    }
    jest.restoreAllMocks();
  });

  it('should be defined on the Object object', () => {
    expect(ObjectAssignPolyfill).toBeDefined();
    expect(typeof ObjectAssignPolyfill).toBe('function');
  });

  it('should assign properties from a single source to a target', () => {
    const target = {};
    const source = { a: 1, b: 2 };
    const result = ObjectAssignPolyfill(target, source);
    expect(result).toBe(target); // Should return the target object
    expect(result).toStrictEqual({ a: 1, b: 2 });
  });

  it('should assign properties from multiple sources to a target', () => {
    const target = { x: 10 };
    const source1 = { a: 1, b: 2 };
    const source2 = { c: 3, d: 4 };
    const result = ObjectAssignPolyfill(target, source1, source2);
    expect(result).toBe(target);
    expect(result).toStrictEqual({ x: 10, a: 1, b: 2, c: 3, d: 4 });
  });

  it('should overwrite properties when sources have the same key', () => {
    const target = { a: 0, b: 0 };
    const source1 = { a: 1, c: 3 };
    const source2 = { b: 2, a: 5 }; // a:5 should overwrite a:1
    const result = ObjectAssignPolyfill(target, source1, source2);
    expect(result).toStrictEqual({ a: 5, b: 2, c: 3 });
  });

  it('should handle primitive sources by wrapping them, and skip null/undefined sources', () => {
    const target = { a: 1 };
    const source1 = null;
    const source2 = undefined;
    const source3 = 'string'; // Primitives are wrapped, and their own enumerable properties are copied.
    const source4 = 123;
    const source5 = true;
    const result = ObjectAssignPolyfill(target, source1, source2, source3, source4, source5);

    // String primitives are wrapped, so their indexed properties are copied.
    // Number and Boolean primitives have no own enumerable properties to copy.
    // Null and Undefined are ignored.
    expect(result).toStrictEqual({
      0: 's',
      1: 't',
      2: 'r',
      3: 'i',
      4: 'n',
      5: 'g',
      a: 1,
    });
  });

  it('should throw TypeError for null target', () => {
    expect(() => ObjectAssignPolyfill(null, { a: 1 })).toThrow(TypeError);
    expect(() => ObjectAssignPolyfill(null)).toThrow(TypeError);
  });

  it('should throw TypeError for undefined target', () => {
    expect(() => ObjectAssignPolyfill(undefined, { a: 1 })).toThrow(TypeError);
    expect(() => ObjectAssignPolyfill(undefined)).toThrow(TypeError);
  });

  it('should convert primitive targets to objects', () => {
    const resultString = ObjectAssignPolyfill('abc', { a: 1 });
    expect(resultString).toBeInstanceOf(String);
    expect(resultString.valueOf()).toBe('abc');
    expect(resultString.a).toBe(1);

    const resultNumber = ObjectAssignPolyfill(123, { b: 2 });
    expect(resultNumber).toBeInstanceOf(Number);
    expect(resultNumber.valueOf()).toBe(123);
    expect(resultNumber.b).toBe(2);

    const resultBoolean = ObjectAssignPolyfill(true, { c: 3 });
    expect(resultBoolean).toBeInstanceOf(Boolean);
    expect(resultBoolean.valueOf()).toBe(true);
    expect(resultBoolean.c).toBe(3);
  });

  it('should only copy enumerable, own properties', () => {
    const target = {};
    const source = Object.create(
      { inherited: 'value' }, // Inherited property
      {
        enumerableOwn: { value: 'enumerable', enumerable: true },
        nonEnumerableOwn: { value: 'non-enumerable', enumerable: false },
      }
    );
    Object.defineProperty(source, 'anotherEnumerableOwn', { value: 'another', enumerable: true });

    const result = ObjectAssignPolyfill(target, source);
    expect(result).toStrictEqual({ enumerableOwn: 'enumerable', anotherEnumerableOwn: 'another' });
    expect(result).not.toHaveProperty('inherited');
    expect(result).not.toHaveProperty('nonEnumerableOwn');
  });

  it('should not copy Symbol properties (ES5 polyfill behavior)', () => {
    const target = {};
    const sym = Symbol('test');
    const source = {
      a: 1,
      [sym]: 'symbol_value',
    };
    const result = ObjectAssignPolyfill(target, source);
    expect(result).toStrictEqual({ a: 1 });
    expect(result[sym]).toBeUndefined();
  });

  it('should handle empty sources', () => {
    const target = { a: 1 };
    const source = {};
    const result = ObjectAssignPolyfill(target, source);
    expect(result).toStrictEqual({ a: 1 });
  });

  it('should handle no sources', () => {
    const target = { a: 1 };
    const result = ObjectAssignPolyfill(target);
    expect(result).toStrictEqual({ a: 1 });
    expect(result).toBe(target);
  });

  it('should not override native Object.assign if it exists', () => {
    // If running in an environment without a native Object.assign, create a mock to simulate its presence.
    // This allows us to verify that the polyfill correctly avoids overriding an existing implementation.
    const nativeAssign = originalObjectAssign || function mockNativeAssign() {};

    let polyfillResult;
    jest.isolateModules(() => {
      global.Object = global.Object || {};
      global.Object.assign = nativeAssign; // Set it back to native for this isolated module
      require('../object-assign.js'); // Load the polyfill
      polyfillResult = global.Object.assign;
    });

    expect(polyfillResult).toBe(nativeAssign); // Should still be the native one
  });
});
