describe('ES6 Promise Polyfill', () => {
  let PromisePolyfill;

  const noop = () => {};

  beforeEach(() => {
    // Use fake timers to control setTimeout for async tests
    jest.useFakeTimers();

    // Isolate the module to ensure the polyfill is re-applied for each test
    jest.isolateModules(() => {
      // Set up a mock window object without a native Promise
      if (global.window && global.window.Promise) {
        delete global.window.Promise;
      }
      global.window = {};

      // Load the polyfill, which will attach itself to window.Promise
      require('../es6-promise.js');
      PromisePolyfill = global.window.Promise;
    });
  });

  afterEach(() => {
    // Clean up the global window object and timers
    delete global.window;
    jest.useRealTimers();
    jest.restoreAllMocks();
  });

  it('should be defined on the window object', () => {
    expect(PromisePolyfill).toBeDefined();
    expect(typeof PromisePolyfill).toBe('function');
  });

  it('should resolve with a value', async () => {
    const promise = new PromisePolyfill((resolve) => {
      resolve('success');
    });

    jest.runAllTimers();
    await expect(promise).resolves.toBe('success');
  });

  it('should reject with a reason', async () => {
    const error = new Error('failure');
    const promise = new PromisePolyfill((_, reject) => {
      reject(error);
    });

    jest.runAllTimers();
    await expect(promise).rejects.toBe(error);
  });

  it('should chain then calls', async () => {
    const promise = new PromisePolyfill((resolve) => {
      resolve(1);
    })
      .then((value) => value + 1)
      .then((value) => value + 1);

    jest.runAllTimers();
    await expect(promise).resolves.toBe(3);
  });

  it('should execute handlers asynchronously', () => {
    const mockFn = jest.fn();
    const promise = new PromisePolyfill((resolve) => {
      resolve();
    });

    promise.then(mockFn);

    expect(mockFn).not.toHaveBeenCalled();
    jest.runAllTimers();
    expect(mockFn).toHaveBeenCalled();
  });

  it('should handle thenable objects', async () => {
    const thenable = {
      then: (resolve) => {
        resolve('thenable resolved');
      },
    };

    const promise = new PromisePolyfill((resolve) => {
      resolve(thenable);
    });

    jest.runAllTimers();
    await expect(promise).resolves.toBe('thenable resolved');
  });

  it('should allow a catch handler to recover from a rejection and chain to a then', async () => {
    const error = new Error('initial rejection');
    const promise = new PromisePolyfill((_, reject) => {
      reject(error);
    }).catch((reason) => {
      expect(reason).toBe(error);
      return 'recovered'; // Return a value to resolve the next promise
    });

    jest.runAllTimers();
    await expect(promise).resolves.toBe('recovered');
  });

  describe('Promise.resolve', () => {
    it('should return a resolved promise with a value', async () => {
      const promise = PromisePolyfill.resolve('resolved');
      jest.runAllTimers();
      await expect(promise).resolves.toBe('resolved');
    });

    it('should return the same promise if given a promise', () => {
      const originalPromise = new PromisePolyfill(() => {});
      const resolvedPromise = PromisePolyfill.resolve(originalPromise);
      expect(resolvedPromise).toBe(originalPromise);
    });
  });

  describe('Promise.reject', () => {
    it('should return a rejected promise with a reason', async () => {
      const error = new Error('rejected');
      const promise = PromisePolyfill.reject(error);
      jest.runAllTimers();
      await expect(promise).rejects.toBe(error);
    });
  });

  describe('Promise.all', () => {
    it('should resolve when all promises in the array resolve', async () => {
      const promises = [
        PromisePolyfill.resolve(1),
        PromisePolyfill.resolve(2),
        PromisePolyfill.resolve(3),
      ];

      const promise = PromisePolyfill.all(promises);
      jest.runAllTimers();
      await expect(promise).resolves.toEqual([1, 2, 3]);
    });

    it('should reject if any promise in the array rejects', async () => {
      const error = new Error('failure');
      const promises = [
        PromisePolyfill.resolve(1),
        PromisePolyfill.reject(error),
        PromisePolyfill.resolve(3),
      ];

      const promise = PromisePolyfill.all(promises);
      jest.runAllTimers();
      await expect(promise).rejects.toBe(error);
    });

    it('should handle an empty array', async () => {
      const promise = PromisePolyfill.all([]);
      jest.runAllTimers();
      await expect(promise).resolves.toEqual([]);
    });

    it('should handle an array with non-promise values', async () => {
      const values = [1, 'two', true];
      const promise = PromisePolyfill.all(values);
      jest.runAllTimers();
      await expect(promise).resolves.toEqual([1, 'two', true]);
    });
  });

  describe('Promise.race', () => {
    it('should resolve with the value of the first promise to resolve', async () => {
      const promises = [
        new PromisePolyfill((resolve) => {
          setTimeout(() => {
            resolve('slow');
          }, 20);
        }),
        new PromisePolyfill((resolve) => {
          setTimeout(() => {
            resolve('fast');
          }, 10);
        }),
      ];

      const promise = PromisePolyfill.race(promises);
      jest.runAllTimers();
      await expect(promise).resolves.toBe('fast');
    });

    it('should reject with the reason of the first promise to reject', async () => {
      const error = new Error('fast failure');
      const promises = [
        new PromisePolyfill((resolve) => {
          setTimeout(() => {
            resolve('slow');
          }, 20);
        }),
        new PromisePolyfill((_, reject) => {
          setTimeout(() => {
            reject(error);
          }, 10);
        }),
      ];

      const promise = PromisePolyfill.race(promises);
      jest.runAllTimers();
      await expect(promise).rejects.toBe(error);
    });

    it('should resolve with the first value if it is not a promise', async () => {
      const values = [
        'immediate',
        new PromisePolyfill((resolve) => {
          setTimeout(() => {
            resolve('later');
          }, 10);
        }),
      ];

      const promise = PromisePolyfill.race(values);
      jest.runAllTimers();
      await expect(promise).resolves.toBe('immediate');
    });
  });

  describe('Edge Cases', () => {
    it('should not resolve or reject more than once', () => {
      const onFulfilled = jest.fn();
      const onRejected = jest.fn();

      const promise = new PromisePolyfill((resolve, reject) => {
        resolve('first');
        resolve('second');
        reject('should not happen');
      });

      promise.then(onFulfilled, onRejected);

      jest.runAllTimers();

      expect(onFulfilled).toHaveBeenCalledTimes(1);
      expect(onFulfilled).toHaveBeenCalledWith('first');
      expect(onRejected).not.toHaveBeenCalled();
    });

    it('should handle executor functions that throw an error', async () => {
      const error = new Error('executor error');
      const promise = new PromisePolyfill(() => {
        throw error;
      });

      jest.runAllTimers();
      await expect(promise).rejects.toBe(error);
    });
  });
});
