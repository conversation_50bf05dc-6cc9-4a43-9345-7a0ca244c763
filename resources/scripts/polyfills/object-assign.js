/**
 * Object.assign() Polyfill.
 *
 * This polyfill is intended **only for Samsung 2016 Smart TVs (Tizen 2.4)**,
 * which do not provide a native implementation of Object.assign.
 *
 * References:
 *   - https://github.com/ryanhefner/Object.assign
 *   - https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/assign
 */
'use strict';

(function () {
  if (typeof Object.assign !== 'function') {
    Object.assign = function (target) {
      if (target === undefined || target === null) {
        throw new TypeError('Cannot convert undefined or null to object');
      }

      var output = Object(target);
      for (var index = 1; index < arguments.length; index++) {
        var source = arguments[index];
        if (source !== undefined && source !== null) {
          for (var nextKey in source) {
            if (Object.prototype.hasOwnProperty.call(source, nextKey)) {
              output[nextKey] = source[nextKey];
            }
          }
        }
      }
      return output;
    };
  }
})();
