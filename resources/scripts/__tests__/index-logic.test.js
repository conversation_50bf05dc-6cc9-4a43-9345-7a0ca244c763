// Helper functions to create fresh mocks for each test
const createMockWindow = () => ({
  performance: { now: jest.fn(() => 0) },
  location: { href: '' },
  // Use jest's fake timers to control setTimeout
  setTimeout: jest.fn(setTimeout),
  clearTimeout: jest.fn(clearTimeout),
  addEventListener: jest.fn(),
  utils: {
    generateUUID: jest.fn(() => 'mock-uuid-123'),
    url: {
      getQueryParams: jest.fn(() => ({})),
      append: jest.fn((url, params) => {
        if (!params) return url;
        const searchParams = new URLSearchParams(params);
        return `${url}?${searchParams.toString()}`;
      }),
    },
    logger: {
      createId: jest.fn(() => 'mock-log-id'),
      addBaseData: jest.fn(),
      append: jest.fn(),
      send: jest.fn(),
      LOG_LEVEL: { INFO: 'info', ERROR: 'error' },
    },
    request: Object.assign(jest.fn(), {
      getRequestType: jest.fn(() => 'initial'),
    }),
  },
});

const createMockDocument = () => ({
  body: {
    addEventListener: jest.fn(),
  },
});

const createMockTizen = () => {
  const mockExit = jest.fn();
  const mockApp = {
    exit: mockExit,
  };
  return {
    application: {
      getCurrentApplication: jest.fn(() => mockApp),
    },
  };
};

describe('index-logic', () => {
  let indexLogic;
  const testConfig = {
    appUrl: 'http://test.app',
    maxAttempts: 3,
    delayBetweenAttempts: 1000,
    linearDelayBetweenAttempts: true,
    requestTimeout: 5000,
  };

  beforeEach(() => {
    jest.clearAllMocks();
    jest.useFakeTimers();

    global.window = createMockWindow();
    global.document = createMockDocument();
    global.window.tizen = createMockTizen();

    // isolateModules ensures it re-runs with our fresh mocks for every test.
    jest.isolateModules(() => {
      require('../index-logic.js');
    });

    indexLogic = global.window.indexLogic;
  });

  afterEach(() => {
    jest.useRealTimers();
  });

  it('should expose an `indexLogic` object on the window', () => {
    expect(indexLogic).toBeDefined();
    expect(indexLogic.init).toBeInstanceOf(Function);
  });

  describe('init', () => {
    it('should define variables, initialize logger, event listeners, and start the application', () => {
      indexLogic.init(testConfig);

      expect(global.window.utils.logger.createId).toHaveBeenCalledTimes(1);
      expect(global.window.utils.generateUUID).toHaveBeenCalledTimes(1);
      expect(global.window.utils.url.getQueryParams).toHaveBeenCalledTimes(1);

      expect(global.document.body.addEventListener).toHaveBeenCalledWith(
        'keydown',
        expect.any(Function)
      );

      // Asserts that the logger was initialized with correct base data
      expect(global.window.utils.logger.addBaseData).toHaveBeenCalledWith({
        containerSession: 'mock-uuid-123',
        level: 'info',
        requestType: 'initial',
      });

      // Asserts that a request was made to validate the app url
      expect(global.window.utils.request).toHaveBeenCalledTimes(1);
    });

    it('should not generate a UUID if containerSession is provided in query params', () => {
      global.window.utils.url.getQueryParams.mockReturnValue({
        containerSession: 'existing-session-456',
      });

      indexLogic.init(testConfig);

      expect(global.window.utils.generateUUID).not.toHaveBeenCalled();

      // Assert: The existing session is used for logging
      expect(global.window.utils.logger.addBaseData).toHaveBeenCalledWith(
        expect.objectContaining({ containerSession: 'existing-session-456' })
      );
    });
  });

  describe('event listeners', () => {
    it('should exit the application on "back" key press', () => {
      indexLogic.init(testConfig);
      const keydownCallback = global.document.body.addEventListener.mock.calls[0][1];
      keydownCallback({ keyCode: 10009 }); // Simulate "back" button
      expect(global.window.tizen.application.getCurrentApplication().exit).toHaveBeenCalledTimes(1);
    });
  });

  describe('application start and retry flow', () => {
    it('should fetch the app and redirect on success', () => {
      global.window.utils.request.mockImplementation(({ success }) =>
        success({ responseData: 'ok' })
      );

      indexLogic.init(testConfig);

      expect(global.window.utils.request).toHaveBeenCalledTimes(1);
      expect(global.window.utils.logger.send).toHaveBeenCalledWith('mock-log-id');
      expect(global.window.location.href).toContain('http://test.app');
    });

    it('should retry on fetch failure with increasing delays when linearDelayBetweenAttempts is true', () => {
      global.window.utils.request.mockImplementation(({ fail }) =>
        fail({ error: 'network error' })
      );

      indexLogic.init(testConfig);
      // First attempt is synchronous in init()

      expect(global.window.utils.request).toHaveBeenCalledTimes(1);
      // After 1st failure, retry is scheduled with delay = 1000 * (1 % 3) = 1000
      expect(global.window.setTimeout).toHaveBeenLastCalledWith(expect.any(Function), 1000);

      jest.runOnlyPendingTimers(); // Second attempt, attemptCounter becomes 2
      expect(global.window.utils.request).toHaveBeenCalledTimes(2);
      // After 2nd failure, retry is scheduled with delay = 1000 * (2 % 3) = 2000
      expect(global.window.setTimeout).toHaveBeenLastCalledWith(expect.any(Function), 2000);
    });

    it('should retry on fetch failure with a fixed delay when linearDelayBetweenAttempts is false', () => {
      global.window.utils.request.mockImplementation(({ fail }) =>
        fail({ error: 'network error' })
      );

      const fixedDelayConfig = { ...testConfig, linearDelayBetweenAttempts: false };
      indexLogic.init(fixedDelayConfig);

      expect(global.window.utils.request).toHaveBeenCalledTimes(1);
      // After 1st failure, retry is scheduled with a fixed delay of 1000
      expect(global.window.setTimeout).toHaveBeenLastCalledWith(expect.any(Function), 1000);

      jest.runOnlyPendingTimers(); // Second attempt
      expect(global.window.utils.request).toHaveBeenCalledTimes(2);
      // After 2nd failure, retry is still scheduled with a fixed delay of 1000
      expect(global.window.setTimeout).toHaveBeenLastCalledWith(expect.any(Function), 1000);
    });

    it('should go to error page after max retries', () => {
      global.window.utils.request.mockImplementation(({ fail }) =>
        fail({ error: 'network error' })
      );

      indexLogic.init(testConfig);
      // Attempt 1 is synchronous in init()

      // Run through remaining attempts
      jest.runOnlyPendingTimers(); // Attempt 2
      jest.runOnlyPendingTimers(); // Attempt 3

      expect(global.window.utils.request).toHaveBeenCalledTimes(3);

      // After the 3rd failure, it should schedule a redirect to the error page
      jest.runOnlyPendingTimers();

      expect(global.window.location.href).toContain('error.html');
      expect(global.window.location.href).toContain('attemptCounter=3');
      expect(global.window.location.href).toContain('containerSession=mock-uuid-123');
    });
  });
});
