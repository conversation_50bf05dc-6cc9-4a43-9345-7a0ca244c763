const fs = require('fs');
const path = require('path');

// Helper to load the script and replace placeholders, simulating a build step
function loadErrorLogicWithUrls(urls) {
  const logicPath = path.resolve(__dirname, '../error-logic.js');
  let source = fs.readFileSync(logicPath, 'utf8');

  // Replace URL placeholders
  source = source.replace(/\${localisationURL}/g, urls.localisationURL || '');
  source = source.replace(/\${labelsURL}/g, urls.labelsURL || '');

  // Execute the modified script in the global scope
  // eslint-disable-next-line no-new-func
  new Function(source)();
}
// Helper functions to create fresh mocks for each test
const createMockWindow = () => ({
  performance: { now: jest.fn(() => 0) },
  location: { href: '' },
  localStorage: {
    getItem: jest.fn(),
    setItem: jest.fn(),
  },
  // Use jest's fake timers to control setTimeout
  setTimeout: jest.fn(setTimeout),
  addEventListener: jest.fn(),
  utils: {
    generateUUID: jest.fn(() => 'mock-uuid-123'),
    url: {
      getQueryParams: jest.fn(() => ({
        containerSession: 'session-123',
        attemptCounter: 3,
        containerStartTime: 1000000,
        appUrl: 'http://the.app.com/failed',
      })),
      append: jest.fn((url, params) => {
        if (!params) return url;
        const searchParams = new URLSearchParams(params);
        return `${url}?${searchParams.toString()}`;
      }),
      getOrigin: jest.fn((url) => new URL(url).origin),
    },
    logger: {
      createId: jest.fn(() => 'mock-error-log-id'),
      addBaseData: jest.fn(),
      append: jest.fn(),
      send: jest.fn(),
      logError: jest.fn(),
      LOG_LEVEL: { INFO: 'info', ERROR: 'error' },
    },
    // Mock the request utility, which also has a `getRequestType` method
    request: Object.assign(jest.fn(), {
      getRequestType: jest.fn(() => 'initial'),
      REQUEST_TYPE: { XHR: 'xhr' },
    }),
  },
  navigator: {
    connection: {
      downlink: 10,
      effectiveType: '4g',
      rtt: 50,
      saveData: false,
      type: 'wifi',
    },
  },
});

const createMockDocument = () => {
  const elements = {}; // Cache for mock elements
  return {
    body: {
      addEventListener: jest.fn(),
    },
    getElementById: jest.fn((id) => {
      if (!elements[id]) {
        elements[id] = {
          id,
          textContent: '',
          innerHTML: '',
          focus: jest.fn(),
        };
      }
      return elements[id];
    }),
  };
};

const createMockTizen = () => {
  const mockExit = jest.fn();
  const mockApp = {
    exit: mockExit,
  };
  return {
    application: {
      getCurrentApplication: jest.fn(() => mockApp),
    },
    systeminfo: {
      getPropertyValue: jest.fn((type, success) => {
        if (type === 'NETWORK') {
          success({ networkType: 'WIFI' });
        } else if (type === 'BUILD') {
          success({ model: 'TIZEN_TV', manufacturer: 'SAMSUNG', buildVersion: '1.0' });
        }
      }),
    },
  };
};

describe('error-logic', () => {
  let errorLogic;

  beforeEach(() => {
    // Reset mocks and use fake timers for each test
    jest.clearAllMocks();
    jest.useFakeTimers();

    // Set up the global scope with fresh mocks before loading the script
    global.window = createMockWindow();
    global.document = createMockDocument();
    global.window.tizen = createMockTizen();

    // The script is an IIFE. We use isolateModules to ensure it re-runs
    // with our fresh mocks for every test.
    jest.isolateModules(() => {
      loadErrorLogicWithUrls({
        localisationURL: 'http://mock.localisation.com',
        labelsURL: 'http://mock.labels.com',
      });
    });

    errorLogic = global.window.errorLogic;
  });

  afterEach(() => {
    jest.useRealTimers();
  });

  it('should expose an `errorLogic` object on the window', () => {
    expect(errorLogic).toBeDefined();
    expect(errorLogic.init).toBeInstanceOf(Function);
  });

  describe('init', () => {
    it('should initialize listeners, logger, and fetch data', () => {
      errorLogic.init();

      expect(global.document.body.addEventListener).toHaveBeenCalledWith(
        'keydown',
        expect.any(Function)
      );
      expect(global.window.utils.logger.addBaseData).toHaveBeenCalledTimes(1);
      expect(global.document.getElementById).toHaveBeenCalledWith('version');
      // Check that localisation and logging were initiated
      expect(global.window.utils.request).toHaveBeenCalledTimes(2);
      expect(global.window.tizen.systeminfo.getPropertyValue).toHaveBeenCalledTimes(2);
    });
  });

  describe('event listeners', () => {
    it('should exit the application on "back" key press', () => {
      errorLogic.init();
      const keydownCallback = global.document.body.addEventListener.mock.calls[0][1];
      keydownCallback({ keyCode: 10009 }); // Simulate "back" button
      expect(global.window.tizen.application.getCurrentApplication().exit).toHaveBeenCalledTimes(1);
    });

    it('should retry on "enter" key press', () => {
      errorLogic.init();
      const keydownCallback = global.document.body.addEventListener.mock.calls[0][1];
      keydownCallback({ keyCode: 13 }); // Simulate "enter" button
      expect(global.window.location.href).toContain('index.html');
      expect(global.window.location.href).toContain('attemptCounter=3');
      expect(global.window.location.href).toContain('containerSession=session-123');
    });
  });

  describe('localisation', () => {
    it('should fetch localisation, then labels, and update the DOM', () => {
      const MOCK_LOCALISATION_URL = 'http://mock.localisation.com';
      const MOCK_LABELS_URL = 'http://mock.labels.com';

      // Mock request implementation to handle different URLs
      global.window.utils.request.mockImplementation(({ url, success }) => {
        if (url === MOCK_LOCALISATION_URL) {
          success({
            ok: true,
            responseData: {
              headers: {
                'x-skyott-language': 'en',
                'x-skyott-territory': 'GB',
              },
            },
          });
        } else if (url === MOCK_LABELS_URL) {
          success({
            responseData: {
              'container.error.title': 'Error Title',
              'container.error.message': 'Error Message',
              'errorPrompt.button.retry': 'Retry Now',
            },
          });
        }
        // Other requests (like testHostName) will be ignored by this mock
      });

      errorLogic.init();

      // Check that localisation was fetched
      expect(global.window.utils.request).toHaveBeenCalledWith(
        expect.objectContaining({ url: MOCK_LOCALISATION_URL })
      );

      // Check that labels were fetched
      expect(global.window.utils.request).toHaveBeenCalledWith(
        expect.objectContaining({ url: MOCK_LABELS_URL })
      );

      // Check that DOM elements were updated
      expect(global.document.getElementById('title').innerHTML).toBe('Error Title');
      expect(global.document.getElementById('message').innerHTML).toBe('Error Message');
      expect(global.document.getElementById('retry').innerHTML).toBe('Retry Now');
    });

    it('should use localStorage as a fallback for localisation', () => {
      global.window.localStorage.getItem.mockImplementation((key) => {
        if (key === 'localisationLanguage') return 'fr';
        if (key === 'localisationTerritory') return 'FR';
        return null;
      });

      // Mock a failed localisation request
      global.window.utils.request.mockImplementation(({ url, fail }) => {
        if (url === 'http://mock.localisation.com') {
          fail({ error: 'network error' });
        }
      });

      errorLogic.init();

      // Check that it fell back to localStorage and fetched labels
      expect(global.window.localStorage.getItem).toHaveBeenCalledWith('localisationLanguage');
      expect(global.window.localStorage.getItem).toHaveBeenCalledWith('localisationTerritory');
      expect(global.window.utils.request).toHaveBeenCalledWith(
        expect.objectContaining({
          url: 'http://mock.labels.com',
          headers: expect.objectContaining({
            'X-SkyOTT-Language': 'fr',
            'X-SkyOTT-Territory': 'FR',
          }),
        })
      );
    });
  });

  describe('logging', () => {
    it('should log error details and system info to New Relic', () => {
      // Mock the network requests made during the logging process.
      // The testHostName function makes a HEAD request which must be handled.
      global.window.utils.request.mockImplementation(({ url, method, success, fail }) => {
        if (url === 'http://mock.localisation.com') {
          // Fail localisation to simplify the test; the fallback is tested elsewhere.
          fail({ error: 'network error' });
        } else if (method === 'HEAD') {
          success({ status: 200 });
        }
      });

      errorLogic.init();

      // Run the async info-gathering jobs
      jest.runAllTimers();

      expect(global.window.utils.logger.append).toHaveBeenCalledWith('mock-error-log-id', {
        level: 'error',
        message: 'Container error occurred',
        step: 'show error page',
        secondsToError: expect.any(Number),
      });

      // Check that system info was appended
      expect(global.window.utils.logger.append).toHaveBeenCalledWith(
        'mock-error-log-id',
        expect.objectContaining({ model: 'TIZEN_TV' })
      );

      // Check that network info was appended
      expect(global.window.utils.logger.append).toHaveBeenCalledWith(
        'mock-error-log-id',
        expect.objectContaining({ networkType: 'WIFI' })
      );

      // Check that the final log was sent
      expect(global.window.utils.logger.send).toHaveBeenCalledWith('mock-error-log-id');
    });
  });
});
