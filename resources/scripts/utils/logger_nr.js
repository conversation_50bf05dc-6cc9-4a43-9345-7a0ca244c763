/**
 * @fileoverview
 * This module provides a logger utility for sending application logs to New Relic.
 *
 * Key functionalities include:
 * - Defining INFO and ERROR log levels.
 * - Automatically including base data with each log:
 * - Allowing customization of base data via `addBaseData`.
 * - Allowing appending of additional data to specific log entries via `append` and `createId`.
 * - Providing a dedicated `logError` function for capturing and sending JavaScript errors.
 * - Sending log data to the New Relic
 */

(function (utils) {
  var LOG_LEVEL = {
      INFO: 'info',
      ERROR: 'error',
    },
    NR_URL = 'https://log-api.newrelic.com/log/v1';

  var capabilities = {
    platformVersion: tizen.systeminfo.getCapability('http://tizen.org/feature/platform.version'),
  };

  var overrideBaseData = {};

  var logId = 0;

  function addBaseData(data) {
    var keys = Object.keys(data);
    for (var i = 0, length = keys.length; i < length; i++) {
      var key = keys[i];
      overrideBaseData[key] = data[key];
    }
  }

  var appendedData = {};
  function append(logId, data) {
    if (logId === undefined) {
      throw new Error('Log ID is required');
    }
    appendedData[logId] = appendedData[logId] || {};
    var keys = Object.keys(data);
    for (var i = 0, length = keys.length; i < length; i++) {
      var key = keys[i];
      appendedData[logId][key] = data[key];
    }
  }

  function createId() {
    return ++logId;
  }

  function getBaseData() {
    return Object.assign(
      {
        containerVersion: '${version}',
        level: LOG_LEVEL.INFO,
        logtype: 'container',
        osName: 'Tizen',
        osVersion: capabilities.platformVersion,
        platform: 'SAMSUNG',
        proposition: '${proposition}',
        provider: '${provider}',
        territory: '${territory}',
      },
      overrideBaseData
    );
  }

  function send(logIdOrData) {
    var logId, data;

    if (typeof logIdOrData === 'object') {
      data = logIdOrData;
    } else {
      logId = logIdOrData;
    }

    var newRelicKey = '${newRelicApiKey}';

    if (newRelicKey) {
      var body = Object.assign({}, getBaseData(), appendedData[logId], data);

      utils.fetch({
        url: NR_URL,
        method: 'POST',
        headers: {
          'Api-Key': newRelicKey,
          'Content-Type': 'application/json',
          Accept: 'application/json',
        },
        body: JSON.stringify(body),
      });

      // eslint-disable-next-line no-console
      console.warn('New Relic ' + body.level.toUpperCase(), body);
    }

    delete appendedData[logId];
  }

  function logError(message, error) {
    send({
      errorMessage: error.message,
      errorName: error.name,
      errorStack: error.stack,
      level: LOG_LEVEL.ERROR,
      message: message,
    });
  }

  utils.logger = {
    LOG_LEVEL: LOG_LEVEL,
    addBaseData: addBaseData,
    append: append,
    createId: createId,
    logError: logError,
    send: send,
  };
})(window.utils || (window.utils = {}));
