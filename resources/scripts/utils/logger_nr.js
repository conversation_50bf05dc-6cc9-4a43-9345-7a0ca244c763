/**
 * @fileoverview
 * This module provides a logger utility for sending application logs to New Relic.
 *
 * Key functionalities include:
 * - Defining INFO and ERROR log levels.
 * - Automatically including base data with each log:
 * - Allowing customization of base data via `addBaseData`.
 * - Allowing appending of additional data to specific log entries via `append` and `createId`.
 * - Providing a dedicated `logError` function for capturing and sending JavaScript errors.
 * - Sending log data to the New Relic
 *
 * Important:
 * - Ensure to use the provided polyfill for Object.assign for compatibility with Samsung 2016.
 */

(function (utils) {
  var NR_URL = 'https://log-api.newrelic.com/log/v1';
  var NR_KEY = '${newRelicApiKey}';

  var LOG_LEVEL = {
    INFO: 'info',
    ERROR: 'error',
  };

  var capabilities = {
    platformVersion: tizen.systeminfo.getCapability('http://tizen.org/feature/platform.version'),
  };

  var overrideBaseData = {};

  var logId = 0;

  function addBaseData(data) {
    var keys = Object.keys(data);
    for (var i = 0, length = keys.length; i < length; i++) {
      var key = keys[i];
      overrideBaseData[key] = data[key];
    }
  }

  var appendedData = {};
  function append(logId, data) {
    if (logId === undefined) {
      throw new Error('Log ID is required');
    }
    appendedData[logId] = appendedData[logId] || {};
    var keys = Object.keys(data);
    for (var i = 0, length = keys.length; i < length; i++) {
      var key = keys[i];
      appendedData[logId][key] = data[key];
    }
  }

  function createId() {
    return ++logId;
  }

  function getBaseData() {
    // eslint-disable-next-line es5/no-es6-static-methods
    return Object.assign(
      {
        containerVersion: '${version}',
        level: LOG_LEVEL.INFO,
        logtype: 'container',
        osName: 'Tizen',
        osVersion: capabilities.platformVersion,
        platform: 'SAMSUNG',
        proposition: '${proposition}',
        provider: '${provider}',
        territory: '${territory}',
      },
      overrideBaseData
    );
  }

  function sendRequest(body) {
    // Do not send a request if the key is missing or is the unreplaced placeholder.
    if (!NR_KEY || NR_KEY.indexOf('${') !== -1) {
      return;
    }

    utils.request({
      url: NR_URL,
      method: 'POST',
      headers: {
        'Api-Key': NR_KEY,
        'Content-Type': 'application/json',
        Accept: 'application/json',
      },
      body: JSON.stringify(body),
    });
  }

  function sendLogData(data) {
    // eslint-disable-next-line es5/no-es6-static-methods
    var body = Object.assign({}, getBaseData(), data);
    sendRequest(body);
  }

  function sendLogId(logId) {
    // eslint-disable-next-line es5/no-es6-static-methods
    var body = Object.assign({}, getBaseData(), appendedData[logId]);
    sendRequest(body);
    delete appendedData[logId];
  }

  function logError(message, error) {
    sendLogData({
      errorMessage: error.message,
      errorName: error.name,
      errorStack: error.stack,
      level: LOG_LEVEL.ERROR,
      message: message,
    });
  }

  function send(logIdOrData) {
    return typeof logIdOrData === 'object' ? sendLogData(logIdOrData) : sendLogId(logIdOrData);
  }

  utils.logger = {
    LOG_LEVEL: LOG_LEVEL,
    addBaseData: addBaseData,
    append: append,
    createId: createId,
    logError: logError,
    send: send,
  };
})(window.utils || (window.utils = {}));
