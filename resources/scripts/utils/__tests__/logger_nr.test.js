describe('logger_nr', () => {
  const mockPlatformVersion = '6.5';
  let mockFetch;
  let consoleWarnSpy;

  beforeEach(() => {
    mockFetch = jest.fn();
    // Mock Tizen API
    global.tizen = {
      systeminfo: {
        getCapability: jest.fn().mockReturnValue(mockPlatformVersion),
      },
    };

    // Set up a mock window object for the browser environment
    global.window = {
      utils: {
        fetch: mockFetch,
      },
    };

    // Spy on console.warn to check calls without polluting test output
    consoleWarnSpy = jest.spyOn(console, 'warn').mockImplementation(() => {});

    // The script is an IIFE that modifies window.utils, so we need to load it for each test
    // after the window object is set up.
    jest.isolateModules(() => {
      require('../logger_nr.js');
    });
  });

  afterEach(() => {
    // Clean up mocks
    delete global.window;
    delete global.tizen;
    jest.restoreAllMocks();
  });

  it('should attach logger object to window.utils', () => {
    expect(window.utils.logger).toBeInstanceOf(Object);
    expect(window.utils.logger.send).toBeInstanceOf(Function);
    expect(window.utils.logger.logError).toBeInstanceOf(Function);
    expect(window.utils.logger.addBaseData).toBeInstanceOf(Function);
    expect(window.utils.logger.append).toBeInstanceOf(Function);
    expect(window.utils.logger.createId).toBeInstanceOf(Function);
    expect(window.utils.logger.LOG_LEVEL).toEqual({
      INFO: 'info',
      ERROR: 'error',
    });
  });

  describe('createId', () => {
    it('should return incrementing IDs', () => {
      expect(window.utils.logger.createId()).toBe(1);
      expect(window.utils.logger.createId()).toBe(2);
      expect(window.utils.logger.createId()).toBe(3);
    });
  });

  describe('append', () => {
    it('should throw an error if logId is not provided', () => {
      expect(() => window.utils.logger.append(undefined, { data: 'test' })).toThrow(
        'Log ID is required'
      );
    });

    it('should store data against a logId', () => {
      const logId = window.utils.logger.createId();
      const dataToAppend = { custom: 'data' };
      window.utils.logger.append(logId, dataToAppend);
      window.utils.logger.send(logId);

      const sentBody = JSON.parse(mockFetch.mock.calls[0][0].body);
      expect(sentBody).toHaveProperty('custom', 'data');
    });
  });

  describe('addBaseData', () => {
    it('should add new properties to the base data for all subsequent logs', () => {
      window.utils.logger.addBaseData({ appName: 'TestApp', sessionId: '123' });
      window.utils.logger.send({ message: 'test log' });

      const sentBody = JSON.parse(mockFetch.mock.calls[0][0].body);
      expect(sentBody).toHaveProperty('appName', 'TestApp');
      expect(sentBody).toHaveProperty('sessionId', '123');
    });

    it('should override existing properties in the base data', () => {
      window.utils.logger.addBaseData({ proposition: 'new-proposition' });
      window.utils.logger.send({ message: 'test log' });

      const sentBody = JSON.parse(mockFetch.mock.calls[0][0].body);
      expect(sentBody).toHaveProperty('proposition', 'new-proposition');
    });
  });

  describe('send', () => {
    const NR_URL = 'https://log-api.newrelic.com/log/v1';
    const newRelicApiKey = '${newRelicApiKey}'; // This is a literal string from the file

    it('should call fetch with the correct URL, method, and headers', () => {
      window.utils.logger.send({ message: 'test' });
      expect(mockFetch).toHaveBeenCalledWith({
        url: NR_URL,
        method: 'POST',
        headers: {
          'Api-Key': newRelicApiKey,
          'Content-Type': 'application/json',
          Accept: 'application/json',
        },
        body: expect.any(String),
      });
    });

    it('should send a log with default base data', () => {
      const messageData = { message: 'Initial log' };
      window.utils.logger.send(messageData);

      const sentBody = JSON.parse(mockFetch.mock.calls[0][0].body);
      expect(sentBody).toEqual(
        expect.objectContaining({
          containerVersion: '${version}',
          level: 'info',
          logtype: 'container',
          osName: 'Tizen',
          osVersion: mockPlatformVersion,
          platform: 'SAMSUNG',
          proposition: '${proposition}',
          provider: '${provider}',
          territory: '${territory}',
          message: 'Initial log',
        })
      );
    });

    it('should combine base data and appended data when sending by ID', () => {
      const logId = window.utils.logger.createId();
      window.utils.logger.addBaseData({ sessionId: 'session-xyz' });
      window.utils.logger.append(logId, { component: 'menu', message: 'Menu opened' });
      window.utils.logger.send(logId);

      const sentBody = JSON.parse(mockFetch.mock.calls[0][0].body);
      expect(sentBody).toEqual(
        expect.objectContaining({
          sessionId: 'session-xyz',
          component: 'menu',
          message: 'Menu opened',
          level: 'info',
        })
      );
    });

    it('should delete appended data after sending', () => {
      const logId = window.utils.logger.createId();
      window.utils.logger.append(logId, { data: 'once' });
      window.utils.logger.send(logId);

      expect(mockFetch).toHaveBeenCalledTimes(1);
      mockFetch.mockClear();

      // Send again with the same ID, the appended data should be gone
      window.utils.logger.send(logId);
      const sentBody = JSON.parse(mockFetch.mock.calls[0][0].body);
      expect(sentBody).not.toHaveProperty('data');
    });

    it('should call console.warn with the log level and body', () => {
      const logData = { message: 'A warning message', level: 'info' };
      window.utils.logger.send(logData);

      const sentBody = JSON.parse(mockFetch.mock.calls[0][0].body);
      expect(consoleWarnSpy).toHaveBeenCalledWith('New Relic INFO', sentBody);
    });
  });

  describe('logError', () => {
    it('should call send with a formatted error object', () => {
      const error = new Error('Something went wrong');
      error.name = 'TestError';
      error.stack = 'stack trace here';
      const message = 'Caught a test error';

      window.utils.logger.logError(message, error);

      expect(mockFetch).toHaveBeenCalledTimes(1);
      const sentBody = JSON.parse(mockFetch.mock.calls[0][0].body);

      expect(sentBody).toEqual(
        expect.objectContaining({
          level: 'error',
          message: message,
          errorMessage: error.message,
          errorName: error.name,
          errorStack: error.stack,
        })
      );

      expect(console.warn).toHaveBeenCalledWith('New Relic ERROR', sentBody);
    });
  });
});
