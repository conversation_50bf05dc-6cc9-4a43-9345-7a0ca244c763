describe('uuid', () => {
  beforeEach(() => {
    // Set up a mock window object for the browser environment
    global.window = {
      utils: {},
    };
    // The script is an IIFE that modifies window.utils, so we need to load it for each test
    // after the window object is set up.
    jest.isolateModules(() => {
      require('../uuid.js');
    });
  });

  afterEach(() => {
    // Clean up the global window object
    delete global.window;
    jest.restoreAllMocks();
  });

  it('should attach generateUUID function to window.utils', () => {
    expect(window.utils.generateUUID).toBeInstanceOf(Function);
  });

  it('should generate a string that conforms to the UUID v4 format', () => {
    const uuid = window.utils.generateUUID();
    // Regex for xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx
    const uuidV4Regex = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    expect(uuid).toMatch(uuidV4Regex);
  });

  it('should generate unique UUIDs on subsequent calls', () => {
    const uuid1 = window.utils.generateUUID();
    const uuid2 = window.utils.generateUUID();
    expect(uuid1).not.toBe(uuid2);
  });

  describe('when an error occurs during generation', () => {
    const mockTimestamp = 1672531200000; // 2023-01-01T00:00:00.000Z

    beforeEach(() => {
      // Mock Date to return a fixed timestamp
      jest.spyOn(global, 'Date').mockImplementation(() => ({
        getTime: () => mockTimestamp,
      }));

      // Mock Math.random to throw an error to trigger the catch block
      jest.spyOn(Math, 'random').mockImplementation(() => {
        throw new Error('Simulated random generation error');
      });

      // Re-isolate and load the module with the new mocks
      global.window = {
        utils: {},
      };
      jest.isolateModules(() => {
        require('../uuid.js');
      });
    });

    it('should fall back to returning the current timestamp as a string', () => {
      const fallbackUuid = window.utils.generateUUID();
      expect(fallbackUuid).toBe(mockTimestamp.toString());
    });
  });
});
