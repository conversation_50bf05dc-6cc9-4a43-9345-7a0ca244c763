describe('request utility', () => {
  let mockFetch;
  let mockXHR;
  let mockLogger;
  let requestUtility; // The module under test

  // Helper to load the fetch utility in an isolated environment
  function loadFetchUtility(useNativeFetch = true) {
    jest.isolateModules(() => {
      // Set up global mocks before requiring the module
      global.window = {
        fetch: useNativeFetch ? mockFetch : undefined, // Control window.fetch availability
        XMLHttpRequest: mockXHR,
        utils: {
          logger: mockLogger,
        },
        // Ensure Promise is available for useFetch timeout logic
        Promise: global.Promise, // Use native Promise for testing fetch path
        // Add setTimeout and clearTimeout for timeout logic in useFetch
        setTimeout: global.setTimeout,
        clearTimeout: global.clearTimeout,
      };

      // Make XMLHttpRequest available in the global scope for the script to use
      global.XMLHttpRequest = mockXHR;

      // Require the module
      require('../request.js');
      requestUtility = global.window.utils.request;
    });
  }

  beforeEach(() => {
    // Reset mocks before each test
    mockFetch = jest.fn();
    mockXHR = jest.fn(() => ({
      open: jest.fn(),
      send: jest.fn(),
      setRequestHeader: jest.fn(),
      onload: null,
      onerror: null,
      ontimeout: null,
      onprogress: null,
      timeout: 0,
      status: 200,
      responseText: '',
      errorCode: 0,
      errorString: '',
      loaded: 0,
      total: 0,
      destroy: jest.fn(), // Mock destroy method
    }));
    mockLogger = {
      logError: jest.fn(),
    };

    // Ensure real timers are used for async operations like timeouts
    jest.useRealTimers();
  });

  afterEach(() => {
    // Clean up global mocks
    delete global.window;
    delete global.XMLHttpRequest;
    jest.restoreAllMocks();
  });

  // --- Test Request Type Detection ---
  describe('request type detection', () => {
    it('should use fetch if window.fetch is available', () => {
      loadFetchUtility(true);
      expect(requestUtility.getRequestType()).toBe('fetch');
    });

    it('should use xhr if window.fetch is not available', () => {
      loadFetchUtility(false);
      expect(requestUtility.getRequestType()).toBe('xhr');
    });
  });

  // --- Test useXhr path ---
  describe('useXhr', () => {
    beforeEach(() => {
      loadFetchUtility(false); // Force XHR path
    });

    it('should make a GET request and call success on 2xx status', async () => {
      const mockSuccess = jest.fn(async (response) => {
        expect(response.message).toBe('Request successful');
        expect(response.ok).toBe(true);
        expect(response.status).toBe(200);
        expect(response.responseData).toBe('mock response');
      });
      const mockFail = jest.fn();

      requestUtility({
        url: '/api/data',
        method: 'GET',
        success: mockSuccess,
        fail: mockFail,
      });

      // Simulate XHR success
      const xhrInstance = mockXHR.mock.results[0].value;
      xhrInstance.status = 200;
      xhrInstance.responseText = 'mock response';
      xhrInstance.onload();

      await Promise.resolve(); // Allow promises to resolve for mockSuccess to be called

      expect(xhrInstance.open).toHaveBeenCalledWith('GET', '/api/data', true);
      expect(xhrInstance.send).toHaveBeenCalledWith(undefined);
      expect(mockSuccess).toHaveBeenCalledTimes(1);
      expect(mockFail).not.toHaveBeenCalled();
      expect(xhrInstance.destroy).toHaveBeenCalledTimes(1);
    });

    it('should make a POST request with body and headers', async () => {
      const mockSuccess = jest.fn((response) => {
        expect(response.status).toBe(200);
      });
      const mockFail = jest.fn(); // Not expected to be called
      const mockBody = JSON.stringify({ key: 'value' });
      const mockHeaders = { 'Content-Type': 'application/json', 'X-Custom': 'header' };

      requestUtility({
        url: '/api/post',
        method: 'POST',
        headers: mockHeaders,
        body: mockBody,
        success: mockSuccess,
        fail: mockFail,
      });

      const xhrInstance = mockXHR.mock.results[0].value;
      xhrInstance.status = 200;
      await Promise.resolve(); // Allow promises to resolve for mockSuccess to be called
      xhrInstance.onload();

      expect(xhrInstance.open).toHaveBeenCalledWith('POST', '/api/post', true);
      expect(xhrInstance.setRequestHeader).toHaveBeenCalledWith('Content-Type', 'application/json');
      expect(xhrInstance.setRequestHeader).toHaveBeenCalledWith('X-Custom', 'header');
      expect(xhrInstance.send).toHaveBeenCalledWith(mockBody);
      expect(mockSuccess).toHaveBeenCalledTimes(1);
      expect(mockFail).not.toHaveBeenCalled();
    });

    it('should call fail on non-2xx status', async () => {
      const mockSuccess = jest.fn();
      const mockFail = jest.fn((response) => {
        expect(response.message).toBe('Request failed');
        expect(response.ok).toBe(false);
        expect(response.status).toBe(404);
        expect(response.responseText).toBe('Not Found');
      });

      requestUtility({
        url: '/api/404',
        method: 'GET',
        success: mockSuccess,
        fail: mockFail,
      });

      const xhrInstance = mockXHR.mock.results[0].value;
      xhrInstance.status = 404;
      xhrInstance.statusText = 'Not Found';
      xhrInstance.responseText = 'Not Found';
      xhrInstance.onload();
      await Promise.resolve(); // Allow promises to resolve for mockFail to be called

      expect(mockSuccess).not.toHaveBeenCalled();
      expect(mockFail).toHaveBeenCalledTimes(1);
    });

    it('should call fail on network error', async () => {
      const mockSuccess = jest.fn(); // Not expected to be called
      const mockFail = jest.fn((response) => {
        expect(response.message).toBe('Request errored');
        expect(response.status).toBe(0); // XHR network errors often have status 0
      });

      requestUtility({
        url: '/api/error',
        method: 'GET',
        success: mockSuccess,
        fail: mockFail,
      });

      const xhrInstance = mockXHR.mock.results[0].value;
      // When a real XHR request fails due to a network error, its status is 0.
      xhrInstance.status = 0;
      await Promise.resolve(); // Allow promises to resolve for mockFail to be called
      xhrInstance.onerror(); // Simulate network error

      expect(mockSuccess).not.toHaveBeenCalled();
      expect(mockFail).toHaveBeenCalledTimes(1);
    });

    it('should call fail on timeout', async () => {
      const mockSuccess = jest.fn();
      const mockFail = jest.fn((response) => {
        expect(response.message).toBe('Request timed out');
        expect(response.timeout).toBe(5000);
        expect(response.status).toBe(0); // Status is 0 on timeout
      });

      requestUtility({
        url: '/api/timeout',
        method: 'GET',
        timeout: 5000,
        success: mockSuccess,
        fail: mockFail,
      });

      const xhrInstance = mockXHR.mock.results[0].value;
      // When a real XHR request times out, its status is 0.
      xhrInstance.status = 0;
      await Promise.resolve(); // Allow promises to resolve for mockFail to be called
      xhrInstance.ontimeout(); // Simulate timeout

      expect(mockSuccess).not.toHaveBeenCalled();
      expect(mockFail).toHaveBeenCalledTimes(1);
    });

    it('should parse JSON response if Accept header is application/json', async () => {
      const mockData = { data: 'json' };
      const mockSuccess = jest.fn((response) => {
        expect(response.responseData).toStrictEqual(mockData);
      });

      requestUtility({
        url: '/api/json',
        method: 'GET',
        headers: { Accept: 'application/json' },
        success: mockSuccess,
      });

      const xhrInstance = mockXHR.mock.results[0].value;
      xhrInstance.status = 200;
      xhrInstance.responseText = JSON.stringify(mockData);
      await Promise.resolve(); // Allow promises to resolve for mockSuccess to be called
      xhrInstance.onload();
    });

    it('should call fail if JSON parsing fails for XHR', () => {
      const mockSuccess = jest.fn();
      const mockFail = jest.fn();

      requestUtility({
        url: '/api/invalid-json',
        method: 'GET',
        headers: { Accept: 'application/json' },
        success: mockSuccess,
        fail: mockFail,
      });

      const xhrInstance = mockXHR.mock.results[0].value;
      xhrInstance.status = 200;
      xhrInstance.responseText = 'this is not json';
      xhrInstance.onload();

      expect(mockSuccess).not.toHaveBeenCalled();
      expect(mockFail).toHaveBeenCalledTimes(1);
      expect(mockFail).toHaveBeenCalledWith(expect.any(SyntaxError));
    });

    it('should not parse JSON if Accept header is not application/json', async () => {
      const mockText = 'plain text response';
      const mockSuccess = jest.fn((response) => {
        expect(response.responseData).toBe(mockText);
      });

      requestUtility({
        url: '/api/text',
        method: 'GET',
        headers: { Accept: 'text/plain' },
        success: mockSuccess,
      });

      const xhrInstance = mockXHR.mock.results[0].value;
      xhrInstance.status = 200;
      xhrInstance.responseText = mockText;
      await Promise.resolve(); // Allow promises to resolve for mockSuccess to be called
      xhrInstance.onload();
    });

    it('should respect async: false for XHR', () => {
      requestUtility({
        url: '/api/sync',
        method: 'GET',
        async: false,
      });

      const xhrInstance = mockXHR.mock.results[0].value;
      expect(xhrInstance.open).toHaveBeenCalledWith('GET', '/api/sync', false);
    });

    it('should call destroy on XHR instance after completion', async () => {
      const mockSuccess = jest.fn();
      requestUtility({
        url: '/api/destroy',
        method: 'GET',
        success: mockSuccess,
      });

      const xhrInstance = mockXHR.mock.results[0].value;
      xhrInstance.status = 200;
      xhrInstance.onload(); // This synchronously calls the success callback and then destroy()

      // Assert after the onload handler has completed
      expect(mockSuccess).toHaveBeenCalledTimes(1);
      expect(xhrInstance.destroy).toHaveBeenCalledTimes(1);
    });
  });

  // --- Test useFetch path ---
  describe('useFetch', () => {
    beforeEach(() => {
      loadFetchUtility(true); // Force Fetch path
    });

    it('should make a GET request and call success on ok response', async () => {
      const mockResponse = {
        ok: true,
        status: 200,
        statusText: 'OK',
        json: () => Promise.resolve({ data: 'fetch' }),
        text: () => Promise.resolve('fetch'),
      };
      mockFetch.mockResolvedValueOnce(mockResponse);

      const mockSuccess = jest.fn();
      const mockFail = jest.fn();

      await requestUtility({
        url: '/api/fetch-data',
        method: 'GET',
        headers: { Accept: 'application/json' },
        success: mockSuccess,
        fail: mockFail,
      });

      expect(mockFetch).toHaveBeenCalledWith(
        '/api/fetch-data',
        expect.objectContaining({ method: 'GET', headers: { Accept: 'application/json' } })
      );
      expect(mockSuccess).toHaveBeenCalledTimes(1);
      expect(mockSuccess).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'Request successful',
          ok: true,
          status: 200,
          responseData: { data: 'fetch' },
        })
      );
      expect(mockFail).not.toHaveBeenCalled();
    });

    it('should call fail on non-ok response', async () => {
      const mockResponse = {
        ok: false,
        status: 404,
        statusText: 'Not Found',
        json: () => Promise.resolve({ error: 'Not Found' }),
        text: () => Promise.resolve('Not Found'),
      };
      mockFetch.mockResolvedValueOnce(mockResponse);

      const mockSuccess = jest.fn();
      const mockFail = jest.fn();

      await requestUtility({
        url: '/api/fetch-404',
        method: 'GET',
        headers: { Accept: 'application/json' },
        success: mockSuccess,
        fail: mockFail,
      });

      expect(mockFetch).toHaveBeenCalledTimes(1);
      expect(mockSuccess).not.toHaveBeenCalled();
      expect(mockFail).toHaveBeenCalledTimes(1);
      expect(mockFail).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'Request failed',
          ok: false,
          status: 404,
          responseData: { error: 'Not Found' },
        })
      );
    });

    it('should make a POST request with body and headers', async () => {
      const mockResponse = {
        ok: true,
        status: 200,
        json: () => Promise.resolve({}),
        text: () => Promise.resolve(''), // Add text() for default parsing
      };
      mockFetch.mockResolvedValueOnce(mockResponse);

      const mockSuccess = jest.fn();
      const mockBody = JSON.stringify({ item: 'new' });
      const mockHeaders = { 'Content-Type': 'application/json' };

      await requestUtility({
        url: '/api/fetch-post',
        method: 'POST',
        headers: mockHeaders,
        body: mockBody,
        success: mockSuccess,
      });

      expect(mockFetch).toHaveBeenCalledWith('/api/fetch-post', {
        body: mockBody,
        headers: mockHeaders,
        method: 'POST',
      });
      expect(mockSuccess).toHaveBeenCalledTimes(1);
    });

    it('should call fail on network error', async () => {
      const mockError = new Error('Network down');
      mockFetch.mockRejectedValueOnce(mockError);

      const mockSuccess = jest.fn();
      const mockFail = jest.fn();

      await requestUtility({
        url: '/api/fetch-network-error',
        method: 'GET',
        success: mockSuccess,
        fail: mockFail,
      });

      expect(mockFetch).toHaveBeenCalledTimes(1);
      expect(mockSuccess).not.toHaveBeenCalled();
      expect(mockFail).toHaveBeenCalledTimes(1);
      expect(mockFail).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'Request errored',
          errorMessage: 'Network down',
        })
      );
    });

    it('should call fail on timeout', async () => {
      // Mock fetch to never resolve
      mockFetch.mockImplementation(() => new Promise(() => {}));

      const mockSuccess = jest.fn();
      const mockFail = jest.fn();

      // We await the promise returned by requestUtility. It will resolve after the
      // timeout is hit and the fail callback is called.
      await requestUtility({
        url: '/api/fetch-timeout',
        method: 'GET',
        timeout: 100, // Short timeout for test
        success: mockSuccess,
        fail: mockFail,
      });

      expect(mockFetch).toHaveBeenCalledTimes(1);
      expect(mockSuccess).not.toHaveBeenCalled();
      expect(mockFail).toHaveBeenCalledTimes(1);
      expect(mockFail).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'Request timed out',
          timeout: 100,
        })
      );
    });

    it('should parse JSON response if Accept header is application/json', async () => {
      const mockData = { data: 'fetch json' };
      const mockResponse = { ok: true, status: 200, json: () => Promise.resolve(mockData) };
      mockFetch.mockResolvedValueOnce(mockResponse);

      const mockSuccess = jest.fn();

      await requestUtility({
        url: '/api/fetch-json',
        method: 'GET',
        headers: { Accept: 'application/json' },
        success: mockSuccess,
      });

      expect(mockSuccess).toHaveBeenCalledWith(
        expect.objectContaining({
          responseData: mockData,
        })
      );
    });

    it('should call fail if JSON parsing fails for Fetch', async () => {
      const parsingError = new SyntaxError('Invalid JSON');
      // Simulate a response where .json() rejects
      const mockResponse = {
        ok: true,
        status: 200,
        json: () => Promise.reject(parsingError),
        text: () => Promise.resolve('invalid json string'), // Fallback for text()
      };
      mockFetch.mockResolvedValueOnce(mockResponse);

      const mockSuccess = jest.fn();
      const mockFail = jest.fn();

      await requestUtility({
        url: '/api/fetch-invalid-json',
        method: 'GET',
        headers: { Accept: 'application/json' },
        success: mockSuccess,
        fail: mockFail,
      });

      expect(mockSuccess).not.toHaveBeenCalled();
      expect(mockFail).toHaveBeenCalledTimes(1);
      expect(mockFail).toHaveBeenCalledWith({
        errorName: 'SyntaxError',
        errorStack: parsingError.stack,
        errorMessage: 'Invalid JSON',
        message: 'Request errored',
      });
    });

    it('should not parse JSON if Accept header is not application/json', async () => {
      const mockText = 'plain text fetch response';
      const mockResponse = { ok: true, status: 200, text: () => Promise.resolve(mockText) };
      mockFetch.mockResolvedValueOnce(mockResponse);

      const mockSuccess = jest.fn();

      await requestUtility({
        url: '/api/fetch-text',
        method: 'GET',
        headers: { Accept: 'text/plain' },
        success: mockSuccess,
      });

      expect(mockSuccess).toHaveBeenCalledWith(
        expect.objectContaining({
          responseData: mockText,
        })
      );
    });
  });
});
