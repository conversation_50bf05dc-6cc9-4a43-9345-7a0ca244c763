/**
 * @fileoverview
 * This module provides a utility function for generating a Version 4 UUID (Universally Unique Identifier).
 * It is attached to the `window.utils` object.
 *
 * The `generateUUID` function creates a UUID string based on the current timestamp and random numbers.
 * It follows the 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx' format.
 * In case of an error during the standard generation process, it falls back to returning
 * the string representation of the current timestamp.
 */

(function (utils) {
  function generateUUID() {
    var uuid; // generated uuid
    var dt = new Date().getTime(); // current date

    try {
      uuid = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
        var r = (dt + Math.random() * 16) % 16 | 0;
        dt = Math.floor(dt / 16);
        return (c === 'x' ? r : (r & 0x3) | 0x8).toString(16);
      });
    } catch (_) {
      uuid = dt.toString();
    }

    return uuid;
  }

  utils.generateUUID = generateUUID;
})(window.utils || (window.utils = {}));
