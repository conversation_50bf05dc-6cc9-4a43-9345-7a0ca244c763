/**
 * @fileoverview
 * This module provides utility functions for making HTTP requests using either
 * `fetch` or `XMLHttpRequest` (XHR), depending on browser/platform support.
 *
 * - If `window.fetch` is available (Samsung 2017+ / Tizen 3.0+), it uses `fetch`.
 * - Otherwise (Samsung 2016 / Tizen 2.4), it falls back to XHR.
 *
 * The correct implementation is chosen automatically based on platform support.
 *
 * Usage:
 *   window.utils.request(params);
 *
 * @param {Object} params - Request configuration object:
 *   @param {string} params.url - The request URL.
 *   @param {string} params.method - HTTP method (e.g., 'GET', 'POST').
 *   @param {Object} [params.headers] - Optional. HTTP headers as key-value pairs.
 *   @param {string|Object} [params.body] - Optional. Request body (for POST/PUT).
 *   @param {number} [params.timeout] - Optional. Timeout in milliseconds.
 *   @param {boolean} [params.async] - Optional. For XHR only, whether the request is asynchronous (default: true).
 *   @param {function(Object)} [params.success] - Optional. Callback for successful requests.
 *   @param {function(Object)} [params.fail] - Optional. Callback for failed requests (response not successful, errors and timeouts)
 */

(function (utils) {
  var STATUS_SUCCESS = 2;

  var REQUEST_TYPE = {
    FETCH: 'fetch',
    XHR: 'xhr',
  };

  var requestType = window.fetch ? REQUEST_TYPE.FETCH : REQUEST_TYPE.XHR;

  function getRequestType() {
    return requestType;
  }

  function parseXhrResponse(xhr, params) {
    var accept = params.headers && params.headers['Accept'];
    var responseText = xhr.responseText;

    if (accept && /^application\/.*json$/i.test(accept)) {
      try {
        return JSON.parse(responseText);
      } catch (error) {
        if (params.fail) {
          params.fail(error);
        }
        return null;
      }
    } else {
      return responseText;
    }
  }

  function safeDestroy(xhr) {
    if (typeof xhr.destroy === 'function') {
      xhr.destroy();
    }
  }

  function calculateProgress(sizeLoaded, sizeTotal) {
    var progress = Math.floor((sizeLoaded / sizeTotal) * 100);
    return sizeTotal && !isNaN(progress) ? progress + '%' : '';
  }

  // XHR is currently used only by Samsung 2016 (Tizen 2.4)
  function useXhr(params) {
    var sizeLoaded, sizeTotal;
    var xhr = new XMLHttpRequest();
    xhr.open(params.method, params.url, params.async !== false); // default to true
    xhr.timeout = params.timeout;

    // Set request headers if provided
    if (params.headers !== undefined) {
      for (var key in params.headers) {
        if (Object.prototype.hasOwnProperty.call(params.headers, key)) {
          xhr.setRequestHeader(key, params.headers[key]);
        }
      }
    }

    xhr.onprogress = function (event) {
      sizeLoaded = event.loaded;
      sizeTotal = event.total; // only available if server sends `Content-Length` header
    };

    xhr.onload = function () {
      var statusType = Math.floor(this.status / 100);

      if (statusType === STATUS_SUCCESS && params.success) {
        var responseData = parseXhrResponse(this, params);
        if (responseData !== null) {
          params.success({
            message: 'Request successful',
            ok: true,
            progress: calculateProgress(sizeLoaded, sizeTotal),
            responseData: responseData,
            sizeLoaded: sizeLoaded,
            sizeTotal: sizeTotal,
            status: this.status,
            statusText: this.statusText,
          });
        }
      } else if (statusType !== STATUS_SUCCESS && params.fail) {
        params.fail({
          errorCode: this.errorCode,
          errorString: this.errorString,
          message: 'Request failed',
          ok: false,
          progress: calculateProgress(sizeLoaded, sizeTotal),
          responseText: this.responseText,
          sizeLoaded: sizeLoaded,
          sizeTotal: sizeTotal,
          status: this.status,
          statusText: this.statusText,
        });
      }

      safeDestroy(this);
    };

    xhr.onerror = function () {
      if (params.fail) {
        params.fail({
          errorCode: this.errorCode,
          errorString: this.errorString,
          message: 'Request errored',
          progress: calculateProgress(sizeLoaded, sizeTotal),
          responseText: this.responseText,
          sizeLoaded: sizeLoaded,
          sizeTotal: sizeTotal,
          status: this.status,
          statusText: this.statusText,
        });
      }

      safeDestroy(this);
    };

    xhr.ontimeout = function () {
      if (params.fail) {
        params.fail({
          errorCode: this.errorCode,
          errorString: this.errorString,
          message: 'Request timed out',
          progress: calculateProgress(sizeLoaded, sizeTotal),
          responseText: this.responseText,
          sizeLoaded: sizeLoaded,
          sizeTotal: sizeTotal,
          status: this.status,
          statusText: this.statusText,
          timeout: params.timeout,
        });
      }

      safeDestroy(this);
    };

    xhr.send(params.body);
  }

  // Fetch is used by Samsung 2017+ (Tizen 3.0+)
  // This implementation uses XHR instead of fetch to avoid Promises entirely
  function useFetch(params) {
    var xhr = new window.XMLHttpRequest();
    var timeoutId;

    function cleanup() {
      if (timeoutId) {
        window.clearTimeout(timeoutId);
        timeoutId = null;
      }
    }

    function handleSuccess(responseData, status, statusText) {
      cleanup();
      if (params.success) {
        params.success({
          message: 'Request successful',
          ok: true,
          responseData: responseData,
          status: status,
          statusText: statusText,
        });
      }
    }

    function handleError(status, statusText, responseData) {
      cleanup();
      if (params.fail) {
        params.fail({
          message: status === 408 ? 'Request timed out' : 'Request failed',
          ok: false,
          responseData: responseData,
          status: status,
          statusText: statusText,
        });
      }
    }

    function handleTimeout() {
      cleanup();
      xhr.abort();
      if (params.fail) {
        params.fail({
          message: 'Request timed out',
          timeout: params.timeout,
          status: 408,
          ok: false,
        });
      }
    }

    // Set up XHR event handlers
    xhr.onreadystatechange = function () {
      if (xhr.readyState === 4) {
        var responseData;
        var accept = params.headers && params.headers['Accept'];

        try {
          if (accept && /^application\/.*json$/i.test(accept)) {
            responseData = JSON.parse(xhr.responseText);
          } else {
            responseData = xhr.responseText;
          }
        } catch (_parseError) {
          handleError(xhr.status, xhr.statusText, xhr.responseText);
          return;
        }

        if (xhr.status >= 200 && xhr.status < 300) {
          handleSuccess(responseData, xhr.status, xhr.statusText);
        } else {
          handleError(xhr.status, xhr.statusText, responseData);
        }
      }
    };

    xhr.onerror = function () {
      handleError(0, 'Network Error', null);
    };

    xhr.onabort = function () {
      handleError(0, 'Request Aborted', null);
    };

    // Set up timeout if specified
    if (params.timeout !== undefined) {
      timeoutId = window.setTimeout(handleTimeout, params.timeout);
    }

    // Open and send the request
    try {
      xhr.open(params.method || 'GET', params.url, true);

      // Set headers
      if (params.headers) {
        for (var header in params.headers) {
          if (params.headers.hasOwnProperty(header)) {
            xhr.setRequestHeader(header, params.headers[header]);
          }
        }
      }

      xhr.send(params.body || null);
    } catch (error) {
      cleanup();
      if (params.fail) {
        params.fail({
          errorName: error.name,
          errorStack: error.stack,
          errorMessage: error.message,
          message: 'Request errored',
          status: 0,
          ok: false,
        });
      }
    }
  }

  utils.request = requestType === REQUEST_TYPE.FETCH ? useFetch : useXhr;
  utils.request.REQUEST_TYPE = REQUEST_TYPE;
  utils.request.getRequestType = getRequestType;
})(window.utils || (window.utils = {}));
