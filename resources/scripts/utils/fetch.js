/**
 * @fileoverview
 * This module provides utility functions for making HTTP requests using either
 * `fetch` or `XMLHttpRequest` (XHR), depending on browser/platform support.
 *
 * - If `window.fetch` is available (Samsung 2017+ / Tizen 3.0+), it uses `fetch`.
 * - Otherwise (e.g., Samsung 2016 / Tizen 2.4), it falls back to XHR.
 *
 * The correct implementation is chosen automatically based on platform support.
 *
 * Usage:
 *   window.utils.fetch(params);
 *
 * @param {Object} params - Request configuration object:
 *   @param {string} params.url - The request URL.
 *   @param {string} params.method - HTTP method (e.g., 'GET', 'POST').
 *   @param {Object} [params.headers] - Optional. HTTP headers as key-value pairs.
 *   @param {string|Object} [params.body] - Optional. Request body (for POST/PUT).
 *   @param {number} [params.timeout] - Optional. Timeout in milliseconds.
 *   @param {boolean} [params.async] - Optional. For XHR only, whether the request is asynchronous (default: true).
 */

(function (utils) {
  var STATUS_SUCCESS = 2;

  var REQUEST_TYPE = {
    FETCH: 'fetch',
    XHR: 'xhr',
  };

  var requestType = window.fetch ? REQUEST_TYPE.FETCH : REQUEST_TYPE.XHR;

  function getRequestType() {
    return requestType;
  }

  function parseXhrResponse(xhr, params) {
    var accept = params.headers && params.headers['Accept'];
    var responseText = xhr.responseText;

    if (accept && /^application\/.*json$/i.test(accept)) {
      try {
        return JSON.parse(responseText);
      } catch (error) {
        utils.logger.logError('Error parsing XHR JSON response', error);
        // Handle null on the consumer side!
        return null;
      }
    } else {
      return responseText;
    }
  }

  // XHR is currently used only by Samsung 2016 (Tizen 2.4)
  function useXhr(params) {
    return new Promise(function (resolve) {
      var sizeLoaded, sizeTotal;
      var xhr = new XMLHttpRequest();
      xhr.open(params.method, params.url, params.async !== false); // default to true
      xhr.timeout = params.timeout;

      // Set request headers if provided
      if (params.headers !== undefined) {
        for (var key in params.headers) {
          if (Object.prototype.hasOwnProperty.call(params.headers, key)) {
            xhr.setRequestHeader(key, params.headers[key]);
          }
        }
      }

      xhr.onprogress = function (event) {
        sizeLoaded = event.loaded;
        sizeTotal = event.total; // only available if server sends `Content-Length` header
      };

      xhr.onload = function () {
        var statusType = Math.floor(this.status / 100);

        if (statusType === STATUS_SUCCESS) {
          var successProgress = Math.floor((sizeLoaded / sizeTotal) * 100);
          resolve({
            message: 'Request successful',
            ok: true,
            progress: sizeTotal && isNaN(successProgress) ? '' : successProgress + '%',
            responseData: parseXhrResponse(this, params),
            sizeLoaded: sizeLoaded,
            sizeTotal: sizeTotal,
            status: this.status,
            statusText: this.statusText,
          });
        } else {
          var failProgress = Math.floor((sizeLoaded / sizeTotal) * 100);
          // always resolve instead of reject to align with fetch
          resolve({
            errorCode: this.errorCode,
            errorString: this.errorString,
            message: 'Request failed',
            ok: false,
            progress: sizeTotal && isNaN(failProgress) ? '' : failProgress + '%',
            responseText: this.responseText,
            sizeLoaded: sizeLoaded,
            sizeTotal: sizeTotal,
            status: this.status,
            statusText: this.statusText,
          });
        }

        if (typeof this.destroy === 'function') {
          this.destroy();
        }
      };

      xhr.onerror = function () {
        var errorProgress = Math.floor((sizeLoaded / sizeTotal) * 100);
        // always resolve instead of reject to align with fetch
        resolve({
          errorCode: this.errorCode,
          errorString: this.errorString,
          message: 'Request errored',
          progress: sizeTotal && isNaN(errorProgress) ? '' : errorProgress + '%',
          responseText: this.responseText,
          sizeLoaded: sizeLoaded,
          sizeTotal: sizeTotal,
          status: this.status,
          statusText: this.statusText,
        });
        if (typeof this.destroy === 'function') {
          this.destroy();
        }
      };

      xhr.ontimeout = function () {
        var timeoutProgress = Math.floor((sizeLoaded / sizeTotal) * 100);
        // always resolve instead of reject to align with fetch
        resolve({
          errorCode: this.errorCode,
          errorString: this.errorString,
          message: 'Request timed out',
          progress: sizeTotal && isNaN(timeoutProgress) ? '' : timeoutProgress + '%',
          responseText: this.responseText,
          sizeLoaded: sizeLoaded,
          sizeTotal: sizeTotal,
          status: this.status,
          statusText: this.statusText,
          timeout: params.timeout,
        });
        if (typeof this.destroy === 'function') {
          this.destroy();
        }
      };

      xhr.send(params.body);
    });
  }

  function parseFetchResponse(params, response) {
    var accept = params.headers && params.headers['Accept'];
    var parsePromise;
    if (accept && /^application\/.*json$/i.test(accept)) {
      try {
        parsePromise = response.json();
      } catch (error) {
        utils.logger.logError('Error parsing fetch JSON response', error);
        // Handle null on the consumer side!
        return null;
      }
    } else {
      parsePromise = response.text();
    }
    // Return a promise that resolves to an object with both response and data
    return parsePromise.then(function (parsedResponseData) {
      return {
        responseData: parsedResponseData,
        response: response,
      };
    });
  }

  // Fetch is used by Samsung 2017+ (Tizen 3.0+)
  function useFetch(params) {
    var timeoutId;

    var fetchPromise = window.fetch(params.url, {
      body: params.body,
      headers: params.headers,
      method: params.method,
    });

    var timeoutPromise;
    if (params.timeout !== undefined) {
      timeoutPromise = new Promise(function (_resolve, reject) {
        timeoutId = setTimeout(function () {
          reject(new Error('Request timed out'));
        }, params.timeout);
      });
    }

    var racePromise =
      params.timeout !== undefined ? Promise.race([fetchPromise, timeoutPromise]) : fetchPromise;

    return racePromise
      .then(function (response) {
        return parseFetchResponse(params, response);
      })
      .then(function (result) {
        if (timeoutId) {
          clearTimeout(timeoutId);
        }
        var response = result.response;
        if (response.ok) {
          return {
            message: 'Request successful',
            ok: true,
            responseData: result.responseData,
            status: response.status,
            statusText: response.statusText,
          };
        }

        return {
          message: 'Request failed',
          ok: false,
          responseText: result.responseData,
          status: response.status,
          statusText: response.statusText,
        };
      })
      .catch(function (error) {
        if (timeoutId) {
          clearTimeout(timeoutId);
        }
        if (error.message === 'Request timed out') {
          throw {
            message: 'Request timed out',
            timeout: params.timeout,
          };
        }
        if (params.onerror) {
          throw {
            errorName: error.name,
            errorStack: error.stack,
            errorMessage: error.message,
            message: 'Request errored',
          };
        }
      });
  }

  utils.fetch = requestType === REQUEST_TYPE.FETCH ? useFetch : useXhr;
  utils.fetch.REQUEST_TYPE = REQUEST_TYPE;
  utils.fetch.getRequestType = getRequestType;
})(window.utils || (window.utils = {}));
