/**
 * @fileoverview
 * This module provides utility functions for working with URL query strings.
 */

(function (utils) {
  function toQueryString(params) {
    var query = [];
    for (var key in params) {
      if (Object.prototype.hasOwnProperty.call(params, key)) {
        query.push(encodeURIComponent(key) + '=' + encodeURIComponent(params[key]));
      }
    }
    return query.join('&');
  }

  /**
   * Appends a query string (generated from the provided params object) to a given URL.
   * It correctly handles whether the URL already has query parameters.
   * @param {string} url The base URL to append the query string to.
   * @param {Object<string, string|number|boolean>} params An object containing key-value pairs
   * to be converted into a query string.
   * @returns {string} The URL with the appended query string, or the original URL if params is empty.
   */
  function append(url, params) {
    var queryString = toQueryString(params);
    if (!queryString) {
      return url;
    }
    var char = url.indexOf('?') === -1 ? '?' : '&';
    return url + char + queryString;
  }

  /**
   * Parses the query string from the current `window.location.search`
   * and returns an object containing the key-value pairs of the parameters.
   * @returns {Object<string, string>} An object where keys are query parameter names
   * and values are their corresponding decoded values.
   */
  function getQueryParams() {
    var queryParams = {};
    window.location.search.substring(1).replace(/([^=&]+)=([^&]*)/g, function (m, key, value) {
      queryParams[decodeURIComponent(key)] = decodeURIComponent(value);
    });
    return queryParams;
  }

  utils.query = {
    append: append,
    getQueryParams: getQueryParams,
  };
})(window.utils || (window.utils = {}));
