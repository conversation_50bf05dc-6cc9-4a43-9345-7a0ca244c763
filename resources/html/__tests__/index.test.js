/**
 * Tests for inline scripts in index.html
 */

const fs = require('fs');
const path = require('path');
const vm = require('vm');

describe('index.html inline scripts', () => {
  let mockDocument;

  beforeEach(() => {
    mockDocument = {
      body: {
        addEventListener: jest.fn(),
      },
      getElementById: jest.fn(() => ({
        focus: jest.fn(),
      })),
    };

    // Set up global environment
    global.document = mockDocument;
  });

  afterEach(() => {
    // Clean up globals
    delete global.document;
  });

  // Since the code is wrapped in an IIFE, we test observable behavior and side effects
  describe('first inline script (App Url validation)', () => {
    const APP_URL = 'http://test.app.com';

    let mainScriptContent;
    let mockWindow;
    let mockUtils;
    let mockTizen;

    // Helper to execute the script and trigger load event
    const executeScriptAndTriggerLoad = () => {
      // Execute the IIFE script
      mainScriptContent = mainScriptContent.replace(/\$\{url\}/g, APP_URL);
      vm.runInNewContext(mainScriptContent, global);

      // Find and execute the load event handler
      const loadCall = mockWindow.addEventListener.mock.calls.find((call) => call[0] === 'load');
      if (loadCall && loadCall[1]) {
        loadCall[1](); // Execute the load handler
      }
    };

    beforeEach(() => {
      jest.useFakeTimers();

      // Read and extract the main inline script content (IIFE)
      const htmlContent = fs.readFileSync(path.join(__dirname, '../index.html'), 'utf8');
      const scriptMatches = htmlContent.matchAll(/<script>([\s\S]*?)<\/script>/g);
      const scripts = Array.from(scriptMatches, (m) => m[1]);
      mainScriptContent = scripts[0] || '';
      let mockLogIdCounter = 0;

      // Set up mock environment
      mockUtils = {
        logger: {
          LOG_LEVEL: { INFO: 'info' },
          addBaseData: jest.fn(),
          append: jest.fn(),
          createId: jest.fn(() => {
            mockLogIdCounter++;
            return `mock-log-id-${mockLogIdCounter}`;
          }),
          send: jest.fn(),
        },
        url: {
          getQueryParams: jest.fn(() => ({})),
          append: jest.fn(
            (url, params) =>
              `${url}?${Object.keys(params) // prettier-ignore
                .map((k) => `${encodeURIComponent(k)}=${encodeURIComponent(params[k])}`)
                .join('&')}`
          ),
        },
        generateUUID: jest.fn(() => 'mock-uuid'),
        request: jest.fn(),
      };

      mockUtils.request.getRequestType = jest.fn(() => 'mock-request-type');

      const mockExit = jest.fn();
      mockTizen = {
        application: {
          getCurrentApplication: jest.fn(() => ({
            exit: mockExit,
          })),
        },
      };

      mockWindow = {
        addEventListener: jest.fn(),
        location: { href: '' },
        performance: { now: jest.fn(() => 100) }, // For containerStartTime calculation
        setTimeout: global.setTimeout, // Use the spied-on fake timer
        tizen: mockTizen,
        utils: mockUtils,
      };

      // Set up global environment
      global.window = mockWindow;
      global.Date = { now: jest.fn(() => 1000) }; // For containerStartTime calculation
    });

    afterEach(() => {
      jest.useRealTimers();

      // Clean up globals
      delete global.window;
      delete global.Date;
    });

    describe('init', () => {
      it('should execute without errors', () => {
        expect(() => {
          vm.runInNewContext(mainScriptContent, global);
        }).not.toThrow();
      });

      it('should initialize the event listeners', () => {
        executeScriptAndTriggerLoad();
        expect(global.document.body.addEventListener).toHaveBeenCalledWith(
          'keydown',
          expect.any(Function)
        );
      });

      it('should initialize the logger', () => {
        executeScriptAndTriggerLoad();
        expect(global.window.utils.logger.createId).toHaveBeenCalledTimes(1);
        expect(global.window.utils.logger.addBaseData).toHaveBeenCalledWith({
          containerSession: 'mock-uuid',
          level: 'info',
          requestType: 'mock-request-type',
        });
      });

      it('should generate a UUID if containerSession is not provided in query params', () => {
        executeScriptAndTriggerLoad();
        expect(global.window.utils.generateUUID).toHaveBeenCalledTimes(1);
        // Assert: The new session is used for logging
        expect(global.window.utils.logger.addBaseData).toHaveBeenCalledWith(
          expect.objectContaining({ containerSession: 'mock-uuid' })
        );
      });

      it('should not generate a UUID if containerSession is provided in query params', () => {
        global.window.utils.url.getQueryParams.mockReturnValue({
          containerSession: 'existing-session-456',
        });

        executeScriptAndTriggerLoad();
        expect(global.window.utils.generateUUID).not.toHaveBeenCalled();
        // Assert: The existing session is used for logging
        expect(global.window.utils.logger.addBaseData).toHaveBeenCalledWith(
          expect.objectContaining({ containerSession: 'existing-session-456' })
        );
      });

      it('should send a request to validate the app url', () => {
        executeScriptAndTriggerLoad();
        expect(global.window.utils.request).toHaveBeenCalledTimes(1);
        expect(global.window.utils.request).toHaveBeenCalledWith({
          url: expect.stringContaining(
            'http://test.app.com?containerStartTime=900&htmlLoadStartTime=1000'
          ),
          method: 'GET',
          fail: expect.any(Function),
          success: expect.any(Function),
          timeout: 10000,
        });
      });
    });

    describe('Event Listeners', () => {
      let keydownHandler;

      beforeEach(() => {
        executeScriptAndTriggerLoad();
        // Get the keydown event handler
        const keydownCall = mockDocument.body.addEventListener.mock.calls.find(
          (call) => call[0] === 'keydown'
        );
        expect(keydownCall).toBeTruthy();
        keydownHandler = keydownCall[1];
      });

      it('should exit the application on "back" key press', () => {
        keydownHandler({ keyCode: 10009 }); // Simulate "back" button
        expect(global.window.tizen.application.getCurrentApplication().exit).toHaveBeenCalledTimes(
          1
        );
      });

      it('should not exit the application on non-back keys press', () => {
        keydownHandler({ keyCode: 13 }); // Enter key
        expect(global.window.tizen.application.getCurrentApplication().exit).not.toHaveBeenCalled();
      });
    });

    describe('App URL Validation and retry flow', () => {
      it('should log attempt information and redirect to the app url on successful validation', () => {
        global.window.utils.request.mockImplementation(({ success }) =>
          success({ responseData: 'ok', ok: true, status: 200 })
        );

        executeScriptAndTriggerLoad();

        expect(global.window.utils.request).toHaveBeenCalledTimes(1);

        expect(global.window.utils.logger.append).toHaveBeenCalledTimes(2);
        expect(global.window.utils.logger.append).toHaveBeenNthCalledWith(1, 'mock-log-id-1', {
          appUrl: 'http://test.app.com?containerStartTime=900&htmlLoadStartTime=1000',
          step: 'attempt 1',
        });
        expect(global.window.utils.logger.append).toHaveBeenNthCalledWith(2, 'mock-log-id-1', {
          ok: true,
          status: 200,
        });

        expect(global.window.location.href).toBe(
          'http://test.app.com?containerStartTime=900&htmlLoadStartTime=1000'
        );
      });

      it('should retry on validation failure with increasing delays when linearDelayBetweenAttempts is true', () => {
        global.window.utils.request.mockImplementation(({ fail }) =>
          fail({ error: 'network error', ok: false, status: 500 })
        );

        executeScriptAndTriggerLoad();
        // First attempt is synchronous on load
        expect(global.window.utils.request).toHaveBeenCalledTimes(1);

        // After 1st failure, retry is scheduled with delay = 2000 * (1 % 3) = 2000
        expect(mockWindow.setTimeout).toHaveBeenLastCalledWith(expect.any(Function), 2000);

        jest.runOnlyPendingTimers(); // Second attempt, attemptCounter becomes 2
        expect(global.window.utils.request).toHaveBeenCalledTimes(2);

        // After 2nd failure, retry is scheduled with delay = 2000 * (2 % 3) = 4000
        expect(mockWindow.setTimeout).toHaveBeenLastCalledWith(expect.any(Function), 4000);
      });

      it('should retry on fetch failure with a fixed delay when linearDelayBetweenAttempts is false', () => {
        // Modify the script to set linear delay to false
        mainScriptContent = mainScriptContent.replace(
          'var LINEAR_DELAY_BETWEEN_ATTEMPTS = true;',
          'var LINEAR_DELAY_BETWEEN_ATTEMPTS = false;'
        );

        global.window.utils.request.mockImplementation(({ fail }) =>
          fail({ error: 'network error' })
        );

        executeScriptAndTriggerLoad();
        expect(global.window.utils.request).toHaveBeenCalledTimes(1);

        // After 1st failure, retry is scheduled with a fixed delay of 2000
        expect(mockWindow.setTimeout).toHaveBeenLastCalledWith(expect.any(Function), 2000);

        jest.runOnlyPendingTimers(); // Second attempt
        expect(global.window.utils.request).toHaveBeenCalledTimes(2);
        // After 2nd failure, retry is still scheduled with a fixed delay of 2000
        expect(mockWindow.setTimeout).toHaveBeenLastCalledWith(expect.any(Function), 2000);
      });

      it('should go to error page after max retries', () => {
        global.window.utils.request.mockImplementation(({ fail }) =>
          fail({ error: 'network error' })
        );

        executeScriptAndTriggerLoad();
        // Attempt 1 is synchronous
        expect(global.window.utils.request).toHaveBeenCalledTimes(1);

        // Run through remaining attempts
        jest.runOnlyPendingTimers(); // Attempt 2
        expect(global.window.utils.request).toHaveBeenCalledTimes(2);

        jest.runOnlyPendingTimers(); // Attempt 3
        expect(global.window.utils.request).toHaveBeenCalledTimes(3);

        // After the 3rd failure, it should schedule a redirect to the error page
        expect(mockWindow.setTimeout).toHaveBeenLastCalledWith(expect.any(Function), 1000);
        jest.runOnlyPendingTimers();

        expect(global.window.location.href).toContain('error.html');
        expect(global.window.location.href).toContain('attemptCounter=3');
        expect(global.window.location.href).toContain('containerSession=mock-uuid');
      });
    });

    describe('App URL Validation', () => {
      it('should make a request to validate the app URL when load event is triggered', () => {
        executeScriptAndTriggerLoad();

        expect(mockUtils.request).toHaveBeenCalledWith({
          url: expect.stringContaining('${url}'),
          timeout: 10000, // REQUEST_TIMEOUT
          method: 'GET',
          fail: expect.any(Function),
          success: expect.any(Function),
        });
      });

      it('should append containerStartTime and htmlLoadStartTime to the URL', () => {
        executeScriptAndTriggerLoad();

        expect(mockUtils.url.append).toHaveBeenCalledWith(
          '${url}',
          expect.objectContaining({
            containerStartTime: expect.any(Number),
            htmlLoadStartTime: expect.any(Number),
          })
        );
      });

      it('should log attempt information', () => {
        executeScriptAndTriggerLoad();

        expect(mockUtils.logger.append).toHaveBeenCalledWith(
          'mock-log-id',
          expect.objectContaining({
            appUrl: expect.any(String),
            step: 'attempt 1',
          })
        );
      });
    });

    describe('Request Success Handling', () => {
      it('should handle successful response correctly', () => {
        executeScriptAndTriggerLoad();

        const requestCall = mockUtils.request.mock.calls[0][0];
        const successHandler = requestCall.success;

        const mockResponse = {
          responseData: 'some data',
          status: 200,
        };

        successHandler(mockResponse);

        expect(mockUtils.logger.append).toHaveBeenCalled();
        expect(mockUtils.logger.send).toHaveBeenCalled();
        expect(mockResponse.responseData).toBeUndefined(); // Should be deleted
        expect(mockWindow.location.href).toEqual(expect.stringContaining('${url}'));
      });
    });

    describe('Request Failure Handling', () => {
      it('should handle failed response correctly', () => {
        executeScriptAndTriggerLoad();

        const requestCall = mockUtils.request.mock.calls[0][0];
        const failHandler = requestCall.fail;

        const mockResponse = {
          error: 'Network error',
          status: 500,
        };

        failHandler(mockResponse);

        expect(mockUtils.logger.append).toHaveBeenCalled();
        expect(mockUtils.logger.send).toHaveBeenCalled();
        // Should schedule a retry or error page redirect
        expect(mockWindow.setTimeout).toHaveBeenCalled();
      });

      it('should schedule retry with appropriate delay on failure', () => {
        executeScriptAndTriggerLoad();

        const requestCall = mockUtils.request.mock.calls[0][0];
        const failHandler = requestCall.fail;

        failHandler({ error: 'Network error' });

        // Should call setTimeout for retry logic
        expect(mockWindow.setTimeout).toHaveBeenCalled();
        const setTimeoutCall = mockWindow.setTimeout.lastCall;
        expect(setTimeoutCall.delay).toBeGreaterThan(0);
      });
    });

    describe('Integration Tests', () => {
      it('should execute complete initialization flow when load event is triggered', () => {
        executeScriptAndTriggerLoad();

        // Verify all initialization steps occurred
        expect(mockUtils.logger.createId).toHaveBeenCalled();
        expect(mockUtils.url.getQueryParams).toHaveBeenCalled();
        expect(mockUtils.logger.addBaseData).toHaveBeenCalled();
        expect(mockDocument.body.addEventListener).toHaveBeenCalledWith(
          'keydown',
          expect.any(Function)
        );
        expect(mockUtils.request).toHaveBeenCalled();
      });

      it('should handle complete success flow', () => {
        executeScriptAndTriggerLoad();

        // Simulate successful request
        const requestCall = mockUtils.request.mock.calls[0][0];
        const successHandler = requestCall.success;

        successHandler({ status: 200, responseData: 'test' });

        expect(mockUtils.logger.send).toHaveBeenCalled();
        expect(mockWindow.location.href).toBeTruthy();
      });

      it('should handle complete failure flow', () => {
        executeScriptAndTriggerLoad();

        // Simulate failed request
        const requestCall = mockUtils.request.mock.calls[0][0];
        const failHandler = requestCall.fail;

        failHandler({ status: 500, error: 'Server error' });

        expect(mockUtils.logger.send).toHaveBeenCalled();
        expect(mockWindow.setTimeout).toHaveBeenCalled();
      });
    });
  });

  describe('second inline script - (Focus script)', () => {
    it('should focus the splash screen element', () => {
      // Extract the second script (focus script)
      const htmlContent = fs.readFileSync(path.join(__dirname, '../index.html'), 'utf8');
      const scriptMatches = htmlContent.matchAll(/<script>([\s\S]*?)<\/script>/g);
      const scripts = Array.from(scriptMatches, (m) => m[1]);
      const focusScript = scripts[1] || '';

      const mockElement = { focus: jest.fn() };
      mockDocument.getElementById.mockReturnValue(mockElement);

      // Execute the focus script
      vm.runInNewContext(focusScript, global);

      expect(mockDocument.getElementById).toHaveBeenCalledWith('js-img');
      expect(mockElement.focus).toHaveBeenCalled();
    });
  });
});
