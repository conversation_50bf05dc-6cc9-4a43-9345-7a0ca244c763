/**
 * Tests for inline scripts in index.html
 */

const fs = require('fs');
const path = require('path');
const vm = require('vm');

describe('index.html inline scripts', () => {
  let mainScriptContent;
  let focusScriptContent;
  let mockWindow;
  let mockDocument;
  let mockUtils;
  let mockTizen;

  // Helper to get the load event handler from the script
  const getLoadHandler = () => {
    // Execute the script to attach the event listener
    vm.runInNewContext(mainScriptContent, global);
    const loadCall = mockWindow.addEventListener.mock.calls.find((call) => call[0] === 'load');
    return loadCall ? loadCall[1] : null;
  };

  beforeEach(() => {
    // Read and extract the inline script contents
    const htmlContent = fs.readFileSync(path.join(__dirname, '../index.html'), 'utf8');
    const scriptMatches = htmlContent.matchAll(/<script>([\s\S]*?)<\/script>/g);
    const scripts = Array.from(scriptMatches, (m) => m[1]);
    mainScriptContent = scripts[0] || '';
    focusScriptContent = scripts[1] || '';

    // Set up mock environment
    mockUtils = {
      logger: {
        createId: jest.fn(() => 'mock-log-id'),
        addBaseData: jest.fn(),
        append: jest.fn(),
        send: jest.fn(),
        LOG_LEVEL: { INFO: 'info' },
      },
      url: {
        getQueryParams: jest.fn(() => ({})),
        append: jest.fn(
          (url, params) =>
            `${url}?${Object.keys(params) // prettier-ignore
              .map((k) => `${encodeURIComponent(k)}=${encodeURIComponent(params[k])}`)
              .join('&')}`
        ),
      },
      generateUUID: jest.fn(() => 'mock-uuid'),
      request: jest.fn(),
    };

    mockUtils.request.getRequestType = jest.fn(() => 'mock-request-type');

    const mockExit = jest.fn();
    mockTizen = {
      application: {
        getCurrentApplication: jest.fn(() => ({
          exit: mockExit,
        })),
      },
    };

    mockDocument = {
      body: {
        addEventListener: jest.fn(),
      },
      getElementById: jest.fn(() => ({
        focus: jest.fn(),
      })),
    };

    mockWindow = {
      utils: mockUtils,
      performance: { now: jest.fn(() => 100) }, // For containerStartTime calculation
      setTimeout: jest.fn((fn) => fn()), // Execute setTimeout callbacks immediately for simplicity
      addEventListener: jest.fn(),
      location: { href: '' },
      tizen: mockTizen,
    };

    // Set up global environment
    global.window = mockWindow;
    global.document = mockDocument;
    global.Date = { now: jest.fn(() => 1000) }; // For containerStartTime calculation
  });

  afterEach(() => {
    // Clean up globals
    delete global.window;
    delete global.document;
    delete global.Date;
    delete global.setTimeout;

    // Clean up script variables
    delete global.APP_URL;
    delete global.MAX_ATTEMPTS;
    delete global.DELAY_BETWEEN_ATTEMPTS;
    delete global.LINEAR_DELAY_BETWEEN_ATTEMPTS;
    delete global.REQUEST_TIMEOUT;
    delete global.delta;
    delete global.containerStartTime;
    delete global.attemptCounter;
    delete global.containerSession;
    delete global.queryParams;
    delete global.utils;
    delete global.LOG_ID;
    delete global.defineVars;
    delete global.initialiseEventListeners;
    delete global.initialiseLogger;
    delete global.goToErrorPage;
    delete global.retryOrError;
    delete global.validateAppUrl;

    jest.clearAllMocks();
  });

  describe('Script execution and variable initialization', () => {
    it('should define global variables correctly', () => {
      // Execute the script content in global scope
      vm.runInNewContext(mainScriptContent, global);

      // Check that variables are defined
      expect(typeof global.APP_URL).toBe('string');
      expect(global.MAX_ATTEMPTS).toBe(3);
      expect(global.DELAY_BETWEEN_ATTEMPTS).toBe(2000);
      expect(global.LINEAR_DELAY_BETWEEN_ATTEMPTS).toBe(true);
      expect(global.REQUEST_TIMEOUT).toBe(10000);
    });

    it('should calculate containerStartTime correctly', () => {
      vm.runInNewContext(mainScriptContent, global);

      expect(typeof global.containerStartTime).toBe('number');
      expect(global.containerStartTime).toBe(900); // 1000 - 100
    });
  });

  describe('defineVars function', () => {
    it('should initialize variables correctly', () => {
      vm.runInNewContext(mainScriptContent, global);

      // Call defineVars
      global.defineVars();

      expect(mockUtils.logger.createId).toHaveBeenCalled();
      expect(mockUtils.url.getQueryParams).toHaveBeenCalled();
      expect(mockUtils.generateUUID).toHaveBeenCalled();
      expect(typeof global.utils).toBe('object');
      expect(typeof global.containerSession).toBe('string');
      expect(global.attemptCounter).toBe(0);
    });

    it('should use existing containerSession from query params', () => {
      mockUtils.url.getQueryParams.mockReturnValue({
        containerSession: 'existing-session',
        attemptCounter: '5',
      });

      vm.runInNewContext(mainScriptContent, global);
      global.defineVars();

      expect(global.containerSession).toBe('existing-session');
      expect(global.attemptCounter).toBe(5);
    });
  });

  describe('initialiseEventListeners function', () => {
    it('should add keydown event listener to document body', () => {
      vm.runInNewContext(mainScriptContent, global);

      global.initialiseEventListeners();

      expect(mockDocument.body.addEventListener).toHaveBeenCalledWith(
        'keydown',
        expect.any(Function)
      );
    });

    it('should handle back key (10009) correctly', () => {
      vm.runInNewContext(mainScriptContent, global);
      global.initialiseEventListeners();

      // Get the event handler
      const eventHandler = mockDocument.body.addEventListener.mock.calls[0][1];

      // Simulate back key press
      const mockEvent = { keyCode: 10009 };
      eventHandler(mockEvent);

      expect(mockWindow.tizen.application.getCurrentApplication).toHaveBeenCalled();
    });
  });

  describe('initialiseLogger function', () => {
    it('should add base data to logger', () => {
      vm.runInNewContext(mainScriptContent, global);
      global.defineVars();

      global.initialiseLogger();

      expect(mockUtils.logger.addBaseData).toHaveBeenCalledWith({
        containerSession: expect.any(String),
        level: 'info',
        requestType: 'mock-request-type',
      });
    });
  });

  describe('goToErrorPage function', () => {
    it('should redirect to error page with correct parameters', () => {
      vm.runInNewContext(mainScriptContent, global);
      global.defineVars();

      const errorParams = { appUrl: 'http://example.com/app' };
      global.goToErrorPage(errorParams);

      expect(mockUtils.url.append).toHaveBeenCalledWith('error.html', {
        appUrl: 'http://example.com/app',
        attemptCounter: 0,
        containerSession: expect.any(String),
        containerStartTime: expect.any(Number),
      });
    });
  });

  describe('retryOrError function', () => {
    beforeEach(() => {
      vm.runInNewContext(mainScriptContent, global);
      global.defineVars();
    });

    it('should go to error page when max attempts reached', () => {
      global.attemptCounter = 3; // MAX_ATTEMPTS = 3, so remainder = 0

      global.retryOrError({ appUrl: 'http://example.com/app' });

      expect(mockWindow.setTimeout).toHaveBeenCalledWith(expect.any(Function), 1000);
    });

    it('should retry with linear delay when attempts remaining', () => {
      global.attemptCounter = 1; // remainder = 1

      global.retryOrError({ appUrl: 'http://example.com/app' });

      expect(mockWindow.setTimeout).toHaveBeenCalledWith(
        expect.any(Function),
        global.DELAY_BETWEEN_ATTEMPTS * 1 // LINEAR_DELAY_BETWEEN_ATTEMPTS = true
      );
    });

    it('should retry with fixed delay when LINEAR_DELAY_BETWEEN_ATTEMPTS is false', () => {
      // Modify the global variable
      global.LINEAR_DELAY_BETWEEN_ATTEMPTS = false;
      global.attemptCounter = 2; // remainder = 2

      global.retryOrError({ appUrl: 'http://example.com/app' });

      expect(mockWindow.setTimeout).toHaveBeenCalledWith(
        expect.any(Function),
        global.DELAY_BETWEEN_ATTEMPTS // Fixed delay
      );
    });
  });

  describe('validateAppUrl function', () => {
    beforeEach(() => {
      vm.runInNewContext(mainScriptContent, global);
      global.defineVars();
    });

    it('should increment attempt counter', () => {
      const initialCounter = global.attemptCounter;

      global.validateAppUrl();

      expect(global.attemptCounter).toBe(initialCounter + 1);
    });

    it('should make request with correct parameters', () => {
      global.validateAppUrl();

      expect(mockUtils.request).toHaveBeenCalledWith({
        url: expect.stringContaining('${url}'),
        timeout: global.REQUEST_TIMEOUT,
        method: 'GET',
        fail: expect.any(Function),
        success: expect.any(Function),
      });
    });

    it('should handle successful response correctly', () => {
      global.validateAppUrl();

      const requestCall = mockUtils.request.mock.calls[0][0];
      const successHandler = requestCall.success;

      const mockResponse = {
        responseData: 'some data',
        status: 200,
      };

      successHandler(mockResponse);

      expect(mockUtils.logger.append).toHaveBeenCalled();
      expect(mockUtils.logger.send).toHaveBeenCalled();
      expect(mockResponse.responseData).toBeUndefined(); // Should be deleted
    });

    it('should handle failed response correctly', () => {
      global.validateAppUrl();

      const requestCall = mockUtils.request.mock.calls[0][0];
      const failHandler = requestCall.fail;

      const mockResponse = {
        error: 'Network error',
        status: 500,
      };

      failHandler(mockResponse);

      expect(mockUtils.logger.append).toHaveBeenCalled();
      expect(mockUtils.logger.send).toHaveBeenCalled();
    });
  });

  describe('Window load event', () => {
    it('should set up load event listener', () => {
      vm.runInNewContext(mainScriptContent, global);

      expect(mockWindow.addEventListener).toHaveBeenCalledWith('load', expect.any(Function));
    });

    it('should call initialization functions on load', () => {
      // Create mock functions
      const mockDefineVars = jest.fn();
      const mockInitialiseEventListeners = jest.fn();
      const mockInitialiseLogger = jest.fn();
      const mockValidateAppUrl = jest.fn();

      // Set up the script with mocked functions
      const modifiedScript = mainScriptContent // Changed scriptContent to mainScriptContent
        .replace('defineVars();', 'mockDefineVars();')
        .replace('initialiseEventListeners();', 'mockInitialiseEventListeners();')
        .replace('initialiseLogger();', 'mockInitialiseLogger();')
        .replace('validateAppUrl();', 'mockValidateAppUrl();');

      // Add mock functions to global scope
      global.mockDefineVars = mockDefineVars;
      global.mockInitialiseEventListeners = mockInitialiseEventListeners;
      global.mockInitialiseLogger = mockInitialiseLogger;
      global.mockValidateAppUrl = mockValidateAppUrl;

      vm.runInNewContext(modifiedScript, global);

      // Get and call the load event handler
      const loadHandler = mockWindow.addEventListener.mock.calls.find(
        (call) => call[0] === 'load'
      )[1];

      loadHandler();

      expect(mockDefineVars).toHaveBeenCalled();
      expect(mockInitialiseEventListeners).toHaveBeenCalled();
      expect(mockInitialiseLogger).toHaveBeenCalled();
      expect(mockValidateAppUrl).toHaveBeenCalled();

      // Clean up
      delete global.mockDefineVars;
      delete global.mockInitialiseEventListeners;
      delete global.mockInitialiseLogger;
      delete global.mockValidateAppUrl;
    });
  });

  describe('Focus script (second inline script)', () => {
    it('should focus the splash screen element', () => {
      const htmlContent = fs.readFileSync(path.join(__dirname, '../index.html'), 'utf8');
      const focusScriptMatch = htmlContent.match(
        /<script>\s*\/\/ This is needed[\s\S]*?<\/script>/
      );
      const focusScript = focusScriptMatch ? focusScriptMatch[0] : '';

      // Extract just the JavaScript content
      const jsContent = focusScript.replace(/<\/?script[^>]*>/g, '').trim();

      const mockElement = { focus: jest.fn() };
      mockDocument.getElementById.mockReturnValue(mockElement);

      // Execute the focus script
      vm.runInNewContext(jsContent, global);

      expect(mockDocument.getElementById).toHaveBeenCalledWith('js-img');
      expect(mockElement.focus).toHaveBeenCalled();
    });
  });
});
