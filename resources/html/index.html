<!doctype html>
<html lang="en-us">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0" />
    <style type="text/css">
      html,
      body {
        margin: 0;
        padding: 0;
        overflow: hidden;
        width: 100%;
        height: 100%;
      }
    </style>
    <script src="../scripts/polyfills/object-assign.js"></script>
    <script src="../scripts/polyfills/es6-promise.js"></script>
    <script src="../scripts/utils/query.js"></script>
    <script src="../scripts/utils/logger_nr.js"></script>
    <script src="../scripts/utils/fetch.js"></script>
    <script src="../scripts/utils/uuid.js"></script>
    <script>
      var myTest = new Promise(function (resolve, reject) {
        setTimeout(resolve, 1000);
      });

      var APP_URL = '${url}';
      var TIMEOUT = 10000;
      var DELAY_BETWEEN_ATTEMPTS = 2000;
      var MAX_ATTEMPTS = 3;

      var LOG_ID = {
        ATTEMPT: utils.logger.createId(),
      };

      var containerStartTime = Date.now() - (performance.now ? Math.round(performance.now()) : 0);

      var queryParams;
      var containerSession;
      var attemptCounter;

      function init() {
        queryParams = utils.query.getQueryParams();
        containerSession = queryParams.containerSession || utils.generateUUID();
        attemptCounter = queryParams.attemptCounter || 0;

        utils.logger.addBaseData({
          containerSession: containerSession,
          level: utils.logger.LOG_LEVEL.INFO,
          requestType: utils.fetch.getRequestType(),
        });

        attemptApplicationStart();
      }

      function retryOrError() {
        var remainder = attemptCounter % MAX_ATTEMPTS;
        if (remainder === 0) {
          // Small delay to allow the last NR request to hopefully hit the server before the location change,
          // or it might be cancelled by the broser and not logged in.
          setTimeout(goToErrorPage, 1000);
        } else {
          setTimeout(attemptApplicationStart, DELAY_BETWEEN_ATTEMPTS * remainder);
        }
      }

      function goToErrorPage() {
        window.location = utils.query.append('error.html', {
          attemptCounter: attemptCounter,
          containerSession: containerSession,
          containerStartTime: containerStartTime,
        });
      }

      function attemptApplicationStart() {
        attemptCounter++;

        var htmlLoadStartTime = Date.now();
        var url = utils.query.append(APP_URL, {
          containerStartTime: containerStartTime,
          htmlLoadStartTime: htmlLoadStartTime,
        });

        utils.logger.append(LOG_ID.ATTEMPT, {
          appUrl: url,
          step: 'attempt ' + attemptCounter,
        });

        function ok(response) {
          var logs = Object.assign({}, response);
          delete logs.responseData;
          utils.logger.append(LOG_ID.ATTEMPT, logs);
          utils.logger.send(LOG_ID.ATTEMPT);
          window.location.href = url;
        }

        function ko(response) {
          utils.logger.append(LOG_ID.ATTEMPT, response);
          utils.logger.send(LOG_ID.ATTEMPT);
          retryOrError();
        }

        utils
          .fetch({
            url: url,
            timeout: TIMEOUT,
            method: 'GET',
          })
          .then(function (response) {
            return response.ok ? ok(response) : ko(response);
          });
      }

      window.addEventListener('load', init);
    </script>
  </head>

  <body style="background-color: black; margin: 0; padding: 0">
    <div
      id="js-img"
      tabindex="-1"
      role=""
      aria-label=""
      style="position: absolute; width: 100%; height: 100%; z-index: 4; background: url(${splashScreen}) center center / ${splashZoom}% no-repeat rgb(0, 0, 0);"
    ></div>
    <script>
      // This is needed to prevent the voice guide to say "document web" when launching the app
      document.getElementById('js-img').focus();

      document.body.addEventListener('keydown', function (e) {
        if (e.keyCode === 10009) {
          // back
          try {
            tizen.application.getCurrentApplication().exit();
          } catch (ignore) {}
        }
      });
    </script>
  </body>
</html>
