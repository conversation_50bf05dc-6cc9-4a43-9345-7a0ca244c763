<!doctype html>
<html lang="en-us">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0" />
    <style type="text/css">
      html,
      body {
        margin: 0;
        padding: 0;
        overflow: hidden;
        width: 100%;
        height: 100%;
      }
    </style>
    <script src="../scripts/polyfills/object-assign.js"></script>
    <script src="../scripts/utils/url.js"></script>
    <script src="../scripts/utils/logger_nr.js"></script>
    <script src="../scripts/utils/request.js"></script>
    <script src="../scripts/utils/uuid.js"></script>
    <script>
      (function () {
        var APP_URL = '${url}';
        var MAX_ATTEMPTS = 3;
        var DELAY_BETWEEN_ATTEMPTS = 2000;
        // If true, the delay between retries increases linearly. If false, a fixed delay is used.
        var LINEAR_DELAY_BETWEEN_ATTEMPTS = true;
        var REQUEST_TIMEOUT = 10000;

        var delta =
          window.performance && window.performance.now ? Math.round(window.performance.now()) : 0;
        var containerStartTime = Date.now() - delta;

        var attemptCounter;
        var containerSession;
        var queryParams;
        var utils;
        var LOG_ID = {};

        function defineVars() {
          utils = window.utils;
          LOG_ID.ATTEMPT = utils.logger.createId();
          queryParams = utils.url.getQueryParams();
          containerSession = queryParams.containerSession || utils.generateUUID();
          attemptCounter = Number(queryParams.attemptCounter) || 0;
        }

        function initialiseEventListeners() {
          document.body.addEventListener('keydown', function (e) {
            if (e.keyCode === 10009) {
              // back
              try {
                window.tizen.application.getCurrentApplication().exit();
              } catch (_) {
                // noop
              }
            }
          });
        }

        function initialiseLogger() {
          utils.logger.addBaseData({
            containerSession: containerSession,
            level: utils.logger.LOG_LEVEL.INFO,
            requestType: utils.request.getRequestType(),
          });
        }

        function goToErrorPage(errorParams) {
          window.location.href = utils.url.append('error.html', {
            appUrl: errorParams.appUrl,
            attemptCounter: attemptCounter,
            containerSession: containerSession,
            containerStartTime: containerStartTime,
          });
        }

        function retryOrError(errorParams) {
          var remainder = attemptCounter % MAX_ATTEMPTS;
          if (remainder === 0) {
            // Small delay to allow the last NR request to hopefully hit the server before the location change,
            // or it might be cancelled by the browser and not logged in.
            window.setTimeout(function () {
              goToErrorPage(errorParams);
            }, 1000);
          } else {
            var delay = DELAY_BETWEEN_ATTEMPTS;
            if (LINEAR_DELAY_BETWEEN_ATTEMPTS) {
              delay *= remainder;
            }
            window.setTimeout(validateAppUrl, delay);
          }
        }

        function validateAppUrl() {
          attemptCounter++;

          var htmlLoadStartTime = Date.now();
          var url = utils.url.append(APP_URL, {
            containerStartTime: containerStartTime,
            htmlLoadStartTime: htmlLoadStartTime,
          });

          utils.logger.append(LOG_ID.ATTEMPT, {
            appUrl: url,
            step: 'attempt ' + attemptCounter,
          });

          function ok(response) {
            delete response.responseData;
            utils.logger.append(LOG_ID.ATTEMPT, response);
            utils.logger.send(LOG_ID.ATTEMPT);
            window.location.href = url;
          }

          function ko(response) {
            utils.logger.append(LOG_ID.ATTEMPT, response);
            utils.logger.send(LOG_ID.ATTEMPT);
            retryOrError({ appUrl: url });
          }

          utils.request({
            url: url,
            timeout: REQUEST_TIMEOUT,
            method: 'GET',
            fail: ko,
            success: ok,
          });
        }

        window.addEventListener('load', function () {
          defineVars();
          initialiseEventListeners();
          initialiseLogger();
          validateAppUrl();
        });
      })();
    </script>
  </head>

  <body style="background-color: black; margin: 0; padding: 0">
    <div
      id="js-img"
      tabindex="-1"
      role=""
      aria-label=""
      style="position: absolute; width: 100%; height: 100%; z-index: 4; background: url(${splashScreen}) center center / ${splashZoom}% no-repeat rgb(0, 0, 0);"
    ></div>
    <script>
      // This is needed to prevent the voice guide to say "document web" when launching the app
      document.getElementById('js-img').focus();
    </script>
  </body>
</html>
