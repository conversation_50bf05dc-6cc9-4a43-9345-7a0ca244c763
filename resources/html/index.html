<!doctype html>
<html lang="en-us">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0" />
    <script src="../scripts/polyfills/object-assign.js"></script>
    <script src="../scripts/utils/url.js"></script>
    <script src="../scripts/utils/logger_nr.js"></script>
    <script src="../scripts/utils/request.js"></script>
    <script src="../scripts/utils/uuid.js"></script>
    <script>
      // Use IIFE to avoid polluting the global scope and having unexpected behaviours
      (function () {
        var APP_URL = '${url}';
        var MAX_ATTEMPTS = 3;
        var DELAY_BETWEEN_ATTEMPTS = 2000;
        // If true, the delay between retries increases linearly. If false, a fixed delay is used.
        var LINEAR_DELAY_BETWEEN_ATTEMPTS = true;
        var REQUEST_TIMEOUT = 10000;

        var delta =
          window.performance && window.performance.now ? Math.round(window.performance.now()) : 0;
        var containerStartTime = Date.now() - delta;

        var attemptCounter = 0;
        var overallAttempts;
        var containerSession;
        var queryParams;
        var utils;
        var LOG_ID = {};

        function defineVars() {
          utils = window.utils;
          LOG_ID.ATTEMPT = utils.logger.createId();
          queryParams = utils.url.getQueryParams();
          containerSession = queryParams.containerSession || utils.generateUUID();
          overallAttempts = Number(queryParams.overallAttempts) || 0;
        }

        function initialiseEventListeners() {
          document.body.addEventListener('keydown', function (e) {
            if (e.keyCode === 10009) {
              // back
              try {
                window.tizen.application.getCurrentApplication().exit();
              } catch (_) {
                // noop
              }
            }
          });
        }

        function initialiseLogger() {
          utils.logger.addBaseData({
            containerSession: containerSession,
            level: utils.logger.LOG_LEVEL.INFO,
            requestType: utils.request.getRequestType(),
          });
        }

        function goToErrorPage(appUrl) {
          window.location.href = utils.url.append('error.html', {
            appUrl: appUrl,
            overallAttempts: overallAttempts + attemptCounter,
            containerSession: containerSession,
            containerStartTime: containerStartTime,
          });
        }

        function retryOrError(appUrl) {
          if (attemptCounter >= MAX_ATTEMPTS) {
            // Small delay to allow the last NR request to hopefully hit the server before the location change,
            // or it might be cancelled by the browser and not logged in.
            window.setTimeout(function () {
              goToErrorPage(appUrl);
            }, 1000);
            return;
          }

          var delay = DELAY_BETWEEN_ATTEMPTS;
          if (LINEAR_DELAY_BETWEEN_ATTEMPTS) {
            delay *= attemptCounter;
          }
          window.setTimeout(validateAppUrl, delay);
        }

        function validateAppUrl() {
          attemptCounter++;

          var htmlLoadStartTime = Date.now();
          var url = utils.url.append(APP_URL, {
            containerStartTime: containerStartTime,
            htmlLoadStartTime: htmlLoadStartTime,
          });

          utils.logger.append(LOG_ID.ATTEMPT, {
            appUrl: url,
            step: 'attempt ' + (overallAttempts + attemptCounter),
          });

          utils.request({
            method: 'GET',
            url: url,
            timeout: REQUEST_TIMEOUT,
            success: function (response) {
              delete response.responseData;
              utils.logger.append(LOG_ID.ATTEMPT, response);
              utils.logger.send(LOG_ID.ATTEMPT);
              window.location.href = url;
            },
            fail: function (response) {
              utils.logger.append(LOG_ID.ATTEMPT, response);
              utils.logger.send(LOG_ID.ATTEMPT);
              retryOrError(url);
            },
          });
        }

        window.addEventListener('load', function () {
          defineVars();
          initialiseEventListeners();
          initialiseLogger();
          validateAppUrl();
        });
      })();
    </script>
  </head>

  <body style="background-color: black; margin: 0; padding: 0">
    <div
      id="js-img"
      tabindex="-1"
      role=""
      aria-label=""
      style="position: absolute; width: 100%; height: 100%; z-index: 4; background: url(${splashScreen}) center center / ${splashZoom}% no-repeat rgb(0, 0, 0);"
    ></div>
    <script>
      // This is needed to prevent the voice guide to say "document web" when launching the app
      document.getElementById('js-img').focus();
    </script>
  </body>
</html>
