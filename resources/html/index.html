<!DOCTYPE html>
<html lang="en-us">

  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0" />
    <style type="text/css">
      html,
      body {
        margin: 0;
        padding: 0;
        overflow: hidden;
        width: 100%;
        height: 100%;
      }

      iframe {
        width: 100%;
        height: 100%;
        border: none;
        display: block;
      }
    </style>
    <script>
      var RESPONSE_TYPE = {
        INFORMATIONAL: 100,
        SUCCESS: 200,
        REDIRECTION: 300,
        CLIENT_ERROR: 400,
        SERVER_ERROR: 500,
      };


      function myFetch(params) {
        console.log('>> myFetch', params);
        if (window.fetch) {
          console.log('>> WILL USE FETCH');

        } else {
          console.log('>> WILL USE XHR');
          var xhr = new XMLHttpRequest();
          xhr.open(params.method, params.url, true);
          xhr.timeout = params.timeout;
          // If the backend responds, even with a 400, it'll call the onload function
          xhr.onload = function (e) {
            xhr.abort();
            var responseType = Math.floor(xhr.status / 100) * 100;
            console.log('>> responseType', responseType);
            if (responseType === RESPONSE_TYPE.SUCCESS) {
              console.log('>> XHR SUCCESS | e:', JSON.stringify(e), ' | xhr: ', JSON.stringify(xhr));
              params.onsuccess && params.onsuccess();
            } else {
              console.log('>> XHR FAILURE | e:', JSON.stringify(e), ' | xhr: ', JSON.stringify(xhr));
              params.onfailure && params.onfailure({
                errorCode: xhr.errorCode,
                errorString: xhr.errorString,
                message: 'Request failed',
                response: xhr.response,
                responseText: xhr.responseText,
                status: xhr.status,
                statusText: xhr.statusText,
                timeout: false
              });
            }
          };
          // This only gets called if the request fails to reach the backend. This is not a 400 or 500 error
          xhr.onerror = function (e) {
            xhr.abort();
            console.log('>> XHR ERROR | e:', JSON.stringify(e), ' | xhr: ', JSON.stringify(xhr));
            params.onerror && params.onerror({
              errorCode: xhr.errorCode,
              errorString: xhr.errorString,
              message: 'Request errored',
              response: xhr.response,
              responseText: xhr.responseText,
              status: xhr.status,
              statusText: xhr.statusText,
              timeout: false,
            });
          };
          xhr.ontimeout = function (e) {
            console.log('>> XHR TIMEOUT | e:', JSON.stringify(e), ' | xhr: ', JSON.stringify(xhr));
            params.ontimeout && params.ontimeout({
              errorCode: xhr.errorCode,
              errorString: xhr.errorString,
              message: 'Request timed out',
              response: xhr.response,
              responseText: xhr.responseText,
              status: xhr.status,
              statusText: xhr.statusText,
              timeout: true,
            });
          };
          xhr.send();
        }
      }

      alert('>> HELLO 48 x');

    </script>
    <script>

      var APP_URL = '${url}';
      var TIMEOUT = 30000;
      // var TIMEOUT = 1;
      var MAX_ATTEMPTS = 6;

      var containerStartTime = Date.now() - (performance.now ? Math.round(performance.now()) : 0);
      var curAttempts = 0;

      // var wrongDomain = 'http://www.ggggggg.it';
      var wrongDomain = 'https://www.google.com';
      var url;
      var iFrame;
      var iFrameTimerId

      function retryOrError(info) {
        if (curAttempts >= MAX_ATTEMPTS) {
          goToErrorPage(info);
        } else {
          curAttempts++;
          // attemptApplicationStart();
          setTimeout(attemptApplicationStart, 10000);
        }
      }

      function goToErrorPage(info) {
        var goto = 'error.html?';
        if (info) {
          goto += 'timeout=' + info.timeout;
          goto += '&statusCode=' + info.statusCode;
          goto += '&message=' + encodeURIComponent(info.message);
          goto += '&retryAttempts=' + curAttempts;
          goto += '&bytesTransferred=' + info.bytesTransferred;
        } else {
          goto += 'timeout=true';
          goto += '&retryAttempts=' + curAttempts;
        }

        if (window.location.search.length) {
          var additionalInfo = window.location.search.slice(1).split('&');
          for (var index = 0; index < additionalInfo.length; index++) {
            goto += '&' + additionalInfo[index];
          }
        }

        window.location = goto;
      }

      function addCustomUrlParameter(url, param) {
        return (url += url.indexOf('?') < 0 ? '?' + param : '&' + param);
      }

      function attemptApplicationStart() {
        console.log('>> attemptApplicationStart');

        var htmlLoadStartTime = Date.now();
        url = addCustomUrlParameter(
          APP_URL,
          'containerStartTime=' + containerStartTime + '&htmlLoadStartTime=' + htmlLoadStartTime
        );


        myFetch({
          url: url,
          timeout: TIMEOUT,
          method: 'GET',
          onsuccess: function (params) {
            console.log('>> FETCH SUCCESS | args:', JSON.stringify(args));
            window.location = url;
          },
          onfailure: function (args) {
            console.log('>> FETCH FAILURE | args:', JSON.stringify(args));
            retryOrError({
              timeout: false,
              statusCode: args.status,
              message: args.statusText,
            });
          },
          onerror: function (args) {
            console.log('>> FETCH ERROR | args:', JSON.stringify(args));
            retryOrError({
              timeout: false,
              statusCode: 0,
              message: args.message,
            });
          },
          ontimeout: function (args) {
            console.log('>> FETCH TIMEOUT | args:', JSON.stringify(args));
            retryOrError();
          },
        })
        // addIFrame();


        // var xhr = new XMLHttpRequest();
        // xhr.open('GET', url, true);
        // xhr.timeout = TIMEOUT;
        // // If the backend responds, even with a 400, it'll call the onload function
        // xhr.onload = function (e) {
        //   if (xhr.status >= 200 && xhr.status < 304) {
        //     console.log('>> XHR SUCCESS | e:', JSON.stringify(e), ' | xhr: ', JSON.stringify(xhr));
        //     // window.location = url;
        //     retryOrError({
        //       timeout: false,
        //       statusCode: xhr.status,
        //       message: xhr.responseText,
        //     });
        //   } else {
        //     console.log('>> XHR FAILURE | e:', JSON.stringify(e), ' | xhr: ', JSON.stringify(xhr));
        //     retryOrError({
        //       timeout: false,
        //       statusCode: xhr.status,
        //       message: xhr.responseText,
        //     });
        //   }
        // };
        // // This only gets called if the request fails to reach the backend. This is not a 400 or 500 error
        // xhr.onerror = function (e) {
        //   console.log('>> XHR ERROR | e:', JSON.stringify(e), ' | xhr: ', JSON.stringify(xhr));
        //   retryOrError({
        //     timeout: false,
        //     statusCode: 0,
        //     message: xhr.responseText ? xhr.responseText : 'Network request failed',
        //     bytesTransferred: xhr.loaded,
        //   });
        // };
        // xhr.ontimeout = function (e) {
        //   console.log('>> XHR TIMEOUT | e:', JSON.stringify(e), ' | xhr: ', JSON.stringify(xhr));
        //   retryOrError();
        // };
        // xhr.send();
      }

      function addIFrame() {
        // alert('>> Add iframe');
        console.log('>> Add iframe', url);
        iFrame = document.createElement('iframe');
        iFrame.width = '100%';
        iFrame.height = '100%';
        // iFrame.src = url;
        iFrame.src = wrongDomain;
        iFrame.onabort = onIFrameAbort;
        iFrame.onload = onIFrameLoad;
        iFrame.onerror = onIFrameError;
        iFrameTimerId = setTimeout(onIFrameTimeout, 10000);
        document.body.appendChild(iFrame);
      }

      function onIFrameTimeout(number) {
        console.log('>> TIMEOUT: ', arguments);
        clearTimeout(iFrameTimerId);
        console.log('>> iFrame.contentWindow.location: ', iFrame.contentWindow.location);
        console.log('>> iFrame.contentWindow: ', iFrame.contentWindow);
        console.log('>> iFrame.contentDocument: ', iFrame.contentDocument);
        console.log('>> iFrame: ', iFrame);
        console.log('>> String: ', iFrame.contentDocument.body.innerHTML);
        // alert(`>> ERROR ${number}`);
        // window.location.href = 'https://www.google.com';
      }

      function onIFrameAbort(number) {
        console.log('>> ABORT: ', arguments);
        clearTimeout(iFrameTimerId);
        // alert(`>> ERROR ${number}`);
        // window.location.href = 'https://www.google.com';
      }

      function onIFrameError(number) {
        console.log('>> ERROR: ', arguments);
        clearTimeout(iFrameTimerId);
        // alert(`>> ERROR ${number}`);
        // window.location.href = 'https://www.google.com';
      }

      function onIFrameLoad() {
        clearTimeout(iFrameTimerId);
        console.log('>> LOAD: ', url, arguments);
        console.log('>> iFrame.contentWindow.location: ', iFrame.contentWindow.location);
        console.log('>> iFrame.contentWindow: ', iFrame.contentWindow);
        console.log('>> iFrame.contentDocument: ', iFrame.contentDocument);
        console.log('>> iFrame: ', iFrame);
        console.log('>> String: ', iFrame.contentDocument.body.innerHTML);
        // alert('>> LOAD')
        if (iFrame.contentWindow.location.href !== url) {
          console.log('>> window.fetch', window.fetch);
          // fetch(wrongDomain, { method: 'HEAD' })
          //   .then((response) => {
          //     console.log('>> response: ', response);
          //     if (response.status !== 200) {
          //       // if (response.status < 200 || response.status >= 300) {
          //       console.log('>> error A:', response.status);
          //     } else {
          //       console.log('>> OK to fetch');
          //     }
          //   })
          //   .catch((error) => {
          //     console.log('>> error B: ', error);
          //   });
        } else {
          document.getElementById('js-img').style.display = 'none';
        }
      }
      alert('>> HELLO 42 a');
    </script>
  </head>

  <body style="background-color: black; margin: 0; padding: 0">
    <div id="js-img" tabindex="-1" role="" aria-label=""
      style="position: absolute; width: 100%; height: 100%; z-index: 4; background: url(${splashScreen}) center center / ${splashZoom}% no-repeat rgb(0, 0, 0);">
    </div>
    <script>
      // This is needed to prevent the voice guide to say "document web" when launching the app
      document.getElementById('js-img').focus();

      document.body.addEventListener('keydown', function (e) {
        if (e.keyCode === 10009) {
          // back
          try {
            tizen.application.getCurrentApplication().exit();
          } catch (ignore) { }
        }
      });

      alert('>> HELLO 42 b');
      console.log('>> HELLO');
      // attemptApplicationStart();
      setTimeout(attemptApplicationStart, 45000);
    </script>
    <script language="javascript" type="text/javascript">
      // var APP_URL = '${url}';
      // var TIMEOUT = 5000;
      // var containerStartTime = Date.now() - (performance.now ? Math.round(performance.now()) : 0);

      // function addCustomUrlParameter(url, param) {
      //   return (url += url.indexOf('?') < 0 ? '?' + param : '&' + param);
      // }

      // var htmlLoadStartTime = Date.now();
      // url = addCustomUrlParameter(
      //   APP_URL,
      //   'containerStartTime=' + containerStartTime + '&htmlLoadStartTime=' + htmlLoadStartTime
      // );
      // // var wrongDomain = 'http://www.ggggggg.it';
      // var wrongDomain = 'https://www.google.com';
      // var url;
      // var iFrame

      // function addIFrame() {
      //   alert('>> Add iframe');
      //   console.log('>> Add iframe');
      //   iFrame = document.createElement('iframe');
      //   iFrame.width = '100%';
      //   iFrame.height = '100%';
      //   iFrame.src = url;
      //   iFrame.onload = onLoad;
      //   iFrame.onerror = () => onError(2);
      //   document.body.appendChild(iFrame);
      // }

      // // window.addEventListener('load', function () {
      // // window.addEventListener('DOMContentLoaded', function () {
      // //   alert('>> DOMContentLoaded');
      // //   setTimeout(addIFrame, 30000);
      // //   // addIFrame();
      // // });

      // // function ready(callbackFunc) {
      // //   if (document.readyState !== 'loading') {
      // //     // Document is already ready, call the callback directly
      // //     alert('>> 1');
      // //     callbackFunc();
      // //   } else if (document.addEventListener) {
      // //     alert('>> 2');
      // //     // All modern browsers to register DOMContentLoaded
      // //     document.addEventListener('DOMContentLoaded', callbackFunc);
      // //   } else {
      // //     alert('>> 3');
      // //     // Old IE browsers
      // //     document.attachEvent('onreadystatechange', function () {
      // //       if (document.readyState === 'complete') {
      // //         callbackFunc();
      // //       }
      // //     });
      // //   }
      // // }

      // // ready(function () {
      // //   alert('>> DOMContentLoaded');
      // //   setTimeout(addIFrame, 30000);
      // //   // addIFrame();
      // // });

      // function onError(number) {
      //   console.log('>> ERROR: ', arguments);
      //   // alert(`>> ERROR ${number}`);
      //   // window.location.href = 'https://www.google.com';
      // }

      // function onLoad() {
      //   console.log('>> LOAD: ', url, arguments);
      //   console.log('>> iFrame.contentWindow.location: ', iFrame.contentWindow.location);
      //   console.log('>> iFrame.contentWindow: ', iFrame.contentWindow);
      //   console.log('>> iFrame.contentDocument: ', iFrame.contentDocument);
      //   console.log('>> iFrame: ', iFrame);
      //   console.log('>> String: ', iFrame.contentDocument.body.innerHTML);
      //   // alert('>> LOAD')
      //   if (iFrame.contentWindow.location.href !== url) {
      //     console.log('>> window.fetch', fetch);
      //     fetch(wrongDomain, { method: 'HEAD' })
      //       .then((response) => {
      //         console.log('>> response: ', response);
      //         if (response.status !== 200) {
      //           // if (response.status < 200 || response.status >= 300) {
      //           console.log('>> error A:', response.status);
      //         } else {
      //           console.log('>> OK to fetch');
      //         }
      //       })
      //       .catch((error) => {
      //         console.log('>> error B: ', error);
      //       });
      //   } else {
      //     document.getElementById('js-img').style.display = 'none';
      //   }

      // }

      // setTimeout(addIFrame, 30000);
    </script>
  </body>

</html>
