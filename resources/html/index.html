<!doctype html>
<html lang="en-us">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0" />
  </head>
  <body style="background-color: black; margin: 0; padding: 0">
    <div
      id="js-img"
      tabindex="-1"
      role=""
      aria-label=""
      style="position: absolute; width: 100%; height: 100%; z-index: 4; background: url(${splashScreen}) center center / ${splashZoom}% no-repeat rgb(0, 0, 0);"
    ></div>
    <script>
      var APP_URL = '${url}';
      var TIMEOUT = 5000;
      var MAX_ATTEMPTS = 6;

      var containerStartTime = Date.now() - (performance.now ? Math.round(performance.now()) : 0);
      var curAttempts = 0;

      function retryOrError(info) {
        if (curAttempts >= MAX_ATTEMPTS) {
          goToErrorPage(info);
        } else {
          curAttempts++;
          attemptApplicationStart();
        }
      }

      function goToErrorPage(info) {
        var goto = 'error.html?';
        if (info) {
          goto += 'timeout=' + info.timeout;
          goto += '&statusCode=' + info.statusCode;
          goto += '&message=' + encodeURIComponent(info.message);
          goto += '&retryAttempts=' + curAttempts;
          goto += '&bytesTransferred=' + info.bytesTransferred;
        } else {
          goto += 'timeout=true';
          goto += '&retryAttempts=' + curAttempts;
        }

        if (window.location.search.length) {
          var additionalInfo = window.location.search.slice(1).split('&');
          for (var index = 0; index < additionalInfo.length; index++) {
            goto += '&' + additionalInfo[index];
          }
        }

        window.location = goto;
      }

      function addCustomUrlParameter(url, param) {
        return (url += url.indexOf('?') < 0 ? '?' + param : '&' + param);
      }

      function attemptApplicationStart() {
        var htmlLoadStartTime = Date.now();
        var url = addCustomUrlParameter(
          APP_URL,
          'containerStartTime=' + containerStartTime + '&htmlLoadStartTime=' + htmlLoadStartTime
        );

        var xhr = new XMLHttpRequest();
        xhr.open('GET', url, true);
        xhr.timeout = TIMEOUT;
        // If the backend responds, even with a 400, it'll call the onload function
        xhr.onload = function () {
          if (xhr.status >= 200 && xhr.status < 304) {
            window.location = url;
          } else {
            retryOrError({
              timeout: false,
              statusCode: xhr.status,
              message: xhr.responseText,
            });
          }
        };
        // This only gets called if the request fails to reach the backend. This is not a 400 or 500 error
        xhr.onerror = function () {
          retryOrError({
            timeout: false,
            statusCode: 0,
            message: xhr.responseText ? xhr.responseText : 'Network request failed',
            bytesTransferred: xhr.loaded,
          });
        };
        xhr.ontimeout = function () {
          retryOrError();
        };
        xhr.send();
      }

      // This is needed to prevent the voice guide to say "document web" when launching the app
      document.getElementById('js-img').focus();

      document.body.addEventListener('keydown', function (e) {
        if (e.keyCode === 10009) {
          // back
          try {
            tizen.application.getCurrentApplication().exit();
          } catch (ignore) {}
        }
      });

      attemptApplicationStart();
    </script>
  </body>
</html>
