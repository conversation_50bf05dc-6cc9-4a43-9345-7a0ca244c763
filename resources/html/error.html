<!doctype html>
<html lang="en-us">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0" />
    <meta name="description" content="Peacock" />
    <link rel="stylesheet" type="text/css" href="../css/style.css" />
    <script src="../scripts/polyfills/object-assign.js"></script>
    <script src="../scripts/polyfills/es6-promise.js"></script>
    <script src="../scripts/utils/query.js"></script>
    <script src="../scripts/utils/logger_nr.js"></script>
    <script src="../scripts/utils/fetch.js"></script>
    <script>
      var APP_URL = '${url}';
      var ERR_SKIP_LOG = 'ERR_SKIP_LOG';

      var LOG_ID = {
        ERROR_PAGE: utils.logger.createId(),
      };

      var errorPageDisplayTime;
      var queryParams;

      function init() {
        errorPageDisplayTime = Date.now() - (performance.now ? Math.round(performance.now()) : 0);
        queryParams = utils.query.getQueryParams();

        utils.logger.addBaseData({
          containerSession: queryParams.containerSession,
          requestType: utils.fetch.getRequestType(),
        });

        printContainerInfo();
        fetchLocalisationAndLabels();
        logErrorToNewRelic();
      }

      function printContainerInfo() {
        var versionElement = document.getElementById('version');
        versionElement.textContent = queryParams.containerSession + ' - ${version}';
      }

      function updateLocalisedElements(labels) {
        var title = document.getElementById('title');
        var message = document.getElementById('message');
        var retry = document.getElementById('retry');

        var errorTitle = labels['container.error.title'];
        if (errorTitle) {
          title.innerHTML = errorTitle;
        }

        var errorMessage = labels['container.error.message'];
        if (errorMessage) {
          message.innerHTML = errorMessage;
        }

        var retryTitle = labels['errorPrompt.button.retry'];
        if (retryTitle) {
          retry.innerHTML = retryTitle;
        }
      }

      function fetchLocalisationAndLabels() {
        fetchLocalisation()
          .then(function (response) {
            var language;
            var territory;

            if (response.ok && response.responseData) {
              var responseData = response.responseData;
              language = responseData.headers['x-skyott-language'];
              territory = responseData.headers['x-skyott-territory'];
              // cache the values
              localStorage.setItem('localisationLanguage', language);
              localStorage.setItem('localisationTerritory', territory);
            } else {
              // Request failed, so do we have a cached localisation to use?
              language = localStorage.getItem('localisationLanguage');
              territory = localStorage.getItem('localisationTerritory');
            }

            if (!language || !territory) {
              return; // In case there's no language or territory do nothing
            }

            return fetchLabels(language, territory);
          })
          .then(function (response) {
            if (!response) {
              return;
            }

            if (!response.ok || !response.responseData) {
              utils.logger.send(
                Object.assign({}, response, {
                  level: utils.logger.LOG_LEVEL.ERROR,
                  message: 'Error fetching labels',
                })
              );
              return;
            }

            updateLocalisedElements(response.responseData);
          })
          .catch(function (error) {
            utils.logger.logError('Error while fetching localisation and labels', error);
          });
      }

      function fetchLocalisation() {
        var url = '${localisationURL}';

        if (!url) {
          return Promise.resolve();
        }

        var headers = {
          'Content-MD5': 'd41d8cd98f00b204e9800998ecf8427e',
          'X-SkyOTT-Territory': 'undefined',
          'X-SkyOTT-Provider': '${provider}',
          Accept: 'application/vnd.localisationinfo.v1+json',
          'X-SkyOTT-Proposition': '${proposition}',
          'X-SkyOTT-Language': 'undefined',
        };

        var appNamespace = '${appNamespace}';
        if (appNamespace) {
          headers['X-SkyOTT-AppNamespace'] = appNamespace;
        }

        // check if we are using forced...
        if ('${url}'.indexOf('forcedLocationID') > -1) {
          headers['X-SkyOTT-Debug'] = '';
          headers['X-SkyOTT-DeviceId'] = 'samsung-nl';
        }

        return utils.fetch({
          headers: headers,
          method: 'GET',
          url: url,
        });
      }

      function fetchLabels(language, territory) {
        var url = '${labelsURL}';

        if (!url) {
          return Promise.resolve();
        }

        return utils.fetch({
          headers: {
            Accept: 'application/json',
            'X-SkyOTT-ActiveTerritory': territory,
            'X-SkyOTT-Device': 'TV',
            'X-SkyOTT-Language': language,
            'X-SkyOTT-Platform': 'SAMSUNG',
            'X-SkyOTT-Proposition': '${proposition}',
            'X-SkyOTT-Provider': '${provider}',
            'X-SkyOTT-Territory': territory,
          },
          method: 'GET',
          url: url,
        });
      }

      function fetchSystemBuildInfo() {
        return new Promise(function (resolve, reject) {
          tizen.systeminfo.getPropertyValue('BUILD', resolve, reject);
        });
      }

      function fetchSystemNetworkInfo() {
        return new Promise(function (resolve, reject) {
          tizen.systeminfo.getPropertyValue('NETWORK', resolve, reject);
        });
      }

      function testHostName() {
        // I found a bug in the implementation of the xhr on Samsung 2016,
        // so that the base url will persist in the subsequent requests.
        // In this way, when validating the url, we receive a 200, and we change the location
        // but this is not the app url, and the page will eventually crash,
        // resulting in a black screen to the user, without us having a chance to catch this and log it.
        // To avoid this, we will skip the test if we are using xhr.
        // Probably, we should remove the whole test of the hostname as it's not giving us much value,
        // In fact I simulated a 404 on the app url, and the hostname gave a 200 on Peacock but a 404 on Showmax.
        // Anyway, keeping it for the time being.
        if (utils.fetch.getRequestType() === utils.fetch.REQUEST_TYPE.XHR) {
          return Promise.resolve();
        }

        // https://tv.clients.peacocktv.com/samsung-2016.html?container=tizen&container_version=6.2.200
        // Becomes https://tv.clients.peacocktv.com
        var matches = APP_URL.match(/^https?\:\/\/([^\/?#]+)(?:[\/?#]|$)/i);
        var hostname = matches && matches[0];

        if (hostname) {
          utils.logger.append(LOG_ID.ERROR_PAGE, {
            hostname: hostname,
          });

          return utils.fetch({
            url: hostname,
            method: 'HEAD',
          });
        }

        return Promise.resolve();
      }

      function logErrorToNewRelic() {
        if (navigator && navigator.connection) {
          utils.logger.append(LOG_ID.ERROR_PAGE, {
            bandwidth: navigator.connection.downlink,
            connectionSpeed: navigator.connection.effectiveType,
            networkType: navigator.connection.type,
            rtt: navigator.connection.rtt,
            saveData: navigator.connection.saveData,
          });
        }

        utils.logger.append(LOG_ID.ERROR_PAGE, {
          level: utils.logger.LOG_LEVEL.ERROR,
          message: 'Container error occurred',
          step: 'show error page',
          secondsToError: (errorPageDisplayTime - queryParams.containerStartTime) / 1000,
        });

        testHostName()
          .then(function (params) {
            if (params) {
              utils.logger.append(LOG_ID.ERROR_PAGE, {
                hostnameStatusCode: params.status,
              });
            }

            return fetchSystemBuildInfo();
          })
          .then(function (systemInfo) {
            utils.logger.append(LOG_ID.ERROR_PAGE, {
              model: systemInfo.model,
              manufacturer: systemInfo.manufacturer,
              buildVersion: systemInfo.buildVersion,
            });

            return fetchSystemNetworkInfo();
          })
          .then(function (networkInfo) {
            utils.logger.append(LOG_ID.ERROR_PAGE, {
              networkType: networkInfo.networkType,
            });
            utils.logger.send(LOG_ID.ERROR_PAGE);
          })
          .catch(function (error) {
            utils.logger.logError('Error while fetching extra logs', error);
            utils.logger.send(LOG_ID.ERROR_PAGE);
          });
      }

      window.addEventListener('load', init);
    </script>
  </head>

  <body>
    <img src="../images/error.jpeg" />
    <div id="wrapper" tabindex="-1" role="" aria-label="">
      <div id="title" class="title">${errorTitle}</div>
      <div id="message" class="message">${errorMessage}</div>
      <img class="logo" src="../images/logo.png" alt="" />
      <span class="version" id="version">${version}</span>
      <button id="retry" class="retrybutton">RETRY</button>
    </div>
    <script>
      // This is needed to prevent the voice guide to say "document web" when launching the app
      document.getElementById('wrapper').focus();

      document.body.addEventListener('keydown', function (e) {
        if (e.keyCode === 10009) {
          // back
          try {
            tizen.application.getCurrentApplication().exit();
          } catch (ignore) {}
        } else if (e.keyCode === 13) {
          // enter
          // window.location = 'index.html?containerSession=' + containerSessionID;
          window.location = utils.query.append('index.html', {
            attemptCounter: queryParams.attemptCounter,
            containerSession: queryParams.containerSession,
          });
        }
      });
    </script>
  </body>
</html>
