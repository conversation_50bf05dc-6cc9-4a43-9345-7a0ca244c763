<!DOCTYPE html>
<html lang="en-us">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0" />
    <meta name="description" content="Peacock" />
    <link rel="stylesheet" type="text/css" href="../css/style.css" />
  </head>
  <body>
    <img src="../images/error.jpeg" />
    <div id="wrapper" tabindex="-1" role="" aria-label="">
      <div id="title" class="title">${errorTitle}</div>
      <div id="message" class="message">${errorMessage}</div>
      <img class="logo" src="../images/logo.png" alt="" />
      <span class="version" id="version">${version}</span>
      <button id="retry" class="retrybutton">RETRY</button>
    </div>
    <script>
      var APP_URL = '${url}';
      var containerSessionID;
      function generateUUID() {
        var uuid; // generated uuid
        var dt = new Date().getTime(); // current date

        try {
          uuid = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
            var r = (dt + Math.random() * 16) % 16 | 0;
            dt = Math.floor(dt / 16);
            return (c == 'x' ? r : (r & 0x3) | 0x8).toString(16);
          });
        } catch (err) {
          uuid = dt.toString();
        }

        return uuid;
      }

      function fetchLocalisation(done) {
        var url = '${localisationURL}';
        if (url) {
          var xhr = new XMLHttpRequest();
          xhr.open('GET', url);
          xhr.setRequestHeader('Content-MD5', 'd41d8cd98f00b204e9800998ecf8427e');
          xhr.setRequestHeader('X-SkyOTT-Territory', 'undefined');
          xhr.setRequestHeader('X-SkyOTT-Provider', '${provider}');
          xhr.setRequestHeader('Accept', 'application/vnd.localisationinfo.v1+json');
          xhr.setRequestHeader('X-SkyOTT-Proposition', '${proposition}');
          xhr.setRequestHeader('X-SkyOTT-Language', 'undefined');

          var appNamespace = '${appNamespace}';
          if (appNamespace) {
            xhr.setRequestHeader('X-SkyOTT-AppNamespace', appNamespace);
          }

          // check if we are using forced...
          if ('${url}'.indexOf('forcedLocationID') > -1) {
            xhr.setRequestHeader('X-SkyOTT-Debug', '');
            xhr.setRequestHeader('X-SkyOTT-DeviceId', 'samsung-nl');
          }

          xhr.onload = function () {
            if (xhr.status >= 200 && xhr.status < 304) {
              done(null, xhr.response);
            } else {
              done({
                status: xhr.status,
                statusText: xhr.statusText,
                response: xhr.responseText,
              });
            }
          };
          xhr.onerror = function () {
            done({
              status: xhr.status,
              statusText: xhr.statusText,
              response: xhr.responseText,
            });
          };
          xhr.send();
        }
      }

      function fetchLabels(language, territory, done) {
        var xhr = new XMLHttpRequest();
        xhr.open('GET', '${labelsURL}');
        xhr.setRequestHeader('X-SkyOTT-Platform', 'SAMSUNG');
        xhr.setRequestHeader('X-SkyOTT-ActiveTerritory', territory);
        xhr.setRequestHeader('X-SkyOTT-Territory', territory);
        xhr.setRequestHeader('X-SkyOTT-Provider', '${provider}');
        xhr.setRequestHeader('X-SkyOTT-Device', 'TV');
        xhr.setRequestHeader('X-SkyOTT-Proposition', '${proposition}');
        xhr.setRequestHeader('X-SkyOTT-Language', language);

        xhr.onload = function () {
          if (xhr.status >= 200 && xhr.status < 304) {
            done(null, xhr.response);
          } else {
            done({
              status: xhr.status,
              statusText: xhr.statusText,
              response: xhr.responseText,
            });
          }
        };
        xhr.onerror = function () {
          done({
            status: xhr.status,
            statusText: xhr.statusText,
            response: xhr.responseText,
          });
        };
        xhr.send();
      }

      /**
       * Fetches the system build information.
       * @param {function({model: string, manufacturer: string, buildVersion: string})} successCallback - The callback function to execute on success.
       * @param {function()} errorCallback - The callback function to execute on error (optional).
       */
      function fetchSystemBuildInfo(successCallback, errorCallback) {
        tizen.systeminfo.getPropertyValue('BUILD', successCallback, errorCallback);
      }

      /**
       * Fetches the system network information.
       * @param {function(string)} successCallback - The callback function to execute on success.
       * @param {function} errorCallback - The callback function to execute on error.
       */
      function fetchSystemNetworkInfo(successCallback, errorCallback) {
        tizen.systeminfo.getPropertyValue('NETWORK', successCallback, errorCallback);
      }

      fetchLocalisation(function (err, localisationResponse) {
        // default values
        var language;
        var territory;

        if (err) {
          // Request failed, so do we have a cached localisation to use?
          language = localStorage.getItem('localisationLanguage');
          territory = localStorage.getItem('localisationTerritory');
        } else {
          // Response returned OK
          var localisationResponseParsed = JSON.parse(localisationResponse);
          language = localisationResponseParsed.headers['x-skyott-language'];
          territory = localisationResponseParsed.headers['x-skyott-territory'];
          // cache the values
          localStorage.setItem('localisationLanguage', language);
          localStorage.setItem('localisationTerritory', territory);
        }

        if (!language || !territory) {
          return; // In case there's no language or territory do nothing
        }

        fetchLabels(language, territory, function (errorLabels, labelsSource) {
          if (errorLabels) {
            // In case of error do nothing
            // TODO: Send error to New Relic
            return;
          }

          var title = document.getElementById('title');
          var message = document.getElementById('message');
          var retry = document.getElementById('retry');

          var labels = JSON.parse(labelsSource);

          var errorTitle = labels['container.error.title'];
          if (errorTitle) {
            title.innerHTML = errorTitle;
          }

          var errorMessage = labels['container.error.message'];
          if (errorMessage) {
            message.innerHTML = errorMessage;
          }

          var retryTitle = labels['errorPrompt.button.retry'];
          if (retryTitle) {
            retry.innerHTML = retryTitle;
          }
        });
      });

      /**
       * Tests the hostname of the APP_URL.
       * @param {function(number)} callback - returns the status code of the request.
       */
      function testHostName(callback) {
        // https://tv.clients.peacocktv.com/samsung-2016.html?container=tizen&container_version=6.2.200
        // Becomes https://tv.clients.peacocktv.com
        var matches = APP_URL.match(/^https?\:\/\/([^\/?#]+)(?:[\/?#]|$)/i);
        var hostname = matches && matches[0];
        if (hostname) {
          var xhr = new XMLHttpRequest();
          xhr.open('HEAD', hostname);
          xhr.onload = function () {
            callback(xhr.status);
          };
          xhr.onerror = function () {
            callback(0);
          };
          xhr.send();
        } else {
          callback(-1);
        }
      }

      function logErrorToNewRelic() {
        var additionalInfo = window.location.search.slice(1).split('&');
        var newRelicKey = '${newRelicApiKey}';

        if (newRelicKey) {
          var body = {
            appUrl: APP_URL,
            platform: 'SAMSUNG',
            proposition: '${proposition}',
            provider: '${provider}',
            containerVersion: '${version}',
            osName: 'Tizen',
            message: 'Container error occurred',
            level: 'error',
            logtype: 'container',
            timestamp: Date.now(),
          };

          for (var index = 0; index < additionalInfo.length; index++) {
            var keyValuePair = additionalInfo[index].split('=');
            body[keyValuePair[0]] = decodeURIComponent(keyValuePair[1]);
          }

          // If the user has retried, the container session ID will be in the query params
          if (body.containerSession) {
            containerSessionID = body.containerSession;
          } else {
            containerSessionID = generateUUID();
            body.containerSession = containerSessionID;
          }

          var versionElement = document.getElementById('version');
          versionElement.textContent = containerSessionID + ' - ${version}';

          if (navigator && navigator.connection) {
            body.networkType = navigator.connection.type;
            body.connectionSpeed = navigator.connection.effectiveType;
            body.rtt = navigator.connection.rtt;
            body.bandwidth = navigator.connection.downlink;
            body.saveData = navigator.connection.saveData;
          }

          var xhr = new XMLHttpRequest();
          xhr.open('POST', 'https://log-api.newrelic.com/log/v1', true);
          xhr.setRequestHeader('Api-Key', newRelicKey);
          xhr.setRequestHeader('Content-Type', 'application/json');

          function sendLog() {
            xhr.send(JSON.stringify(body));
          }

          try {
            testHostName(function (statusCode) {
              body.hostnameStatusCode = statusCode;
              fetchSystemBuildInfo(function (systemInfo) {
                body.model = systemInfo.model;
                body.manufacturer = systemInfo.manufacturer;
                body.buildVersion = systemInfo.buildVersion;

                fetchSystemNetworkInfo(function (networkInfo) {
                  body.networkType = networkInfo.networkType;
                  sendLog();
                }, sendLog);
              }, sendLog);
            });
          } catch (error) {
            sendLog();
          }

          console.warn('New Relic Error', body);
        }
      }

      // This is needed to prevent the voice guide to say "document web" when launching the app
      document.getElementById('wrapper').focus();

      document.body.addEventListener('keydown', function (e) {
        if (e.keyCode === 10009) {
          // back
          try {
            tizen.application.getCurrentApplication().exit();
          } catch (ignore) {}
        } else if (e.keyCode === 13) {
          // enter
          window.location = 'index.html?containerSession=' + containerSessionID;
        }
      });

      logErrorToNewRelic();
    </script>
  </body>
</html>
