var https = require('https');
var filesStore = require('../utils/file.js');
var cwHelper = require('../utils/cw_calls.js');
var log = require('../utils/logger_newrelic.js');

var TOKENS_HASH_FILE_PATH = 'tokens_hash_pck_4.json';
var ONE_DAY_IN_MILLISECONDS = 86400000; //1000 * 60 * 60 * 24
var DAYS_USING_STORAGE_DATA = 1;

function getSharedAppData() {
  var reqAppControl = tizen.application.getCurrentApplication().getRequestedAppControl();
  if (
    reqAppControl.appControl.operation === 'http://tizen.org/appcontrol/operation/launch_cw_service'
  ) {
    var appData = { revokeAccess: false };
    var appControlData = reqAppControl.appControl.data;

    for (var i = 0; i < appControlData.length; i++) {
      if (appControlData[i].key === 'appData') {
        appData.userToken = appControlData[i].value[0];
        appData.personaId = appControlData[i].value[1];
      }

      if (appControlData[i].key === 'revokeAccess') {
        appData.revokeAccess = true;
      }
    }
    return appData;
  }
  return {};
}

function extractAuthCode(locationHeader) {
  return locationHeader.substring(locationHeader.indexOf('=') + 1);
}

function authorizeUser(userToken) {
  log.logMessage('token-acquisition', 'Authorizing user');
  return new Promise(function (resolve, reject) {
    if (!userToken) {
      return reject(new Error('Missing user token'));
    }
    var options = {
      hostname: '${authUrl}',
      path: '/oauth2/authorize?scope=CONTINUE_WATCHING:READ&response_type=code&client_id=${clientId}',
      method: 'GET',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'X-Token': 'OAuth2 ' + userToken,
        'x-skyint-requestid': log.containerSession,
      },
    };

    var req = https.request(options, function (res) {
      if (res.headers.location) {
        var authCode = extractAuthCode(res.headers.location);
        resolve(authCode);
      } else {
        reject(new Error('Cannot get authorization code'));
      }
    });

    req.on('error', function (error) {
      reject(new Error('Cannot get authorization code -> ' + error.message));
    });

    req.end();
  });
}

function getNewCWToken(personaId, authCode, refresh) {
  var step = refresh ? 'token-refresh' : 'token-acquisition';
  log.logMessage(step, 'Attempting CW ' + step);

  return new Promise(function (resolve, reject) {
    if (!authCode) {
      return reject(new Error('Missing authentication token'));
    }
    if (!personaId) {
      return reject(new Error('Missing personaId'));
    }

    var clientToken = '${clientToken}';
    var grant_type = 'authorization_code';
    var code = 'code';

    if (refresh) {
      grant_type = 'refresh_token';
      code = 'refresh_token';
    }

    var postData =
      'grant_type=' + grant_type + '&redirect_uri=nbcu://auth&' + code + '=' + authCode;

    var options = {
      hostname: '${authUrl}',
      path: '/oauth2/token',
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Content-Length': postData.length,
        Authorization: 'Basic ' + clientToken,
        'X-SkyOTT-Persona': personaId,
        'x-skyint-requestid': log.containerSession,
      },
    };

    var req = https.request(options, function (res) {
      var data = '';
      res.on('data', function (chunk) {
        data += chunk.toString();
      });

      res.on('end', function () {
        if (res.statusCode === 200) {
          try {
            log.logMessage(step, 'Attempting to parse CW token from API');
            var response = JSON.parse(data);

            if (response.code === 'invalid_grant') {
              return reject(new Error('invalid_grant'));
            }

            if (!response.access_token) {
              return reject(new Error('Missing access_token from auth response'));
            }

            if (!response.refresh_token) {
              return reject(new Error('Missing refresh_token from auth response'));
            }

            log.logMessage(step, 'Successfully received tokens from API');

            var newTokens = {
              access_token: response.access_token,
              refresh_token: response.refresh_token,
              personaId: personaId,
              timestamp: Date.now(),
            };

            filesStore
              .replaceFile(TOKENS_HASH_FILE_PATH, JSON.stringify(newTokens))
              .then(
                function () {
                  log.logMessage(step, 'Data successfully saved');
                  resolve(newTokens);
                  return;
                },
                function (error) {
                  reject('Error while saving data file ->' + error.message);
                }
              )
              .catch(function (error) {
                reject('Error while saving data file ->' + error.message);
              });
          } catch (error) {
            return reject(new Error('Unable to parse backend response -> ' + error.message));
          }
        } else {
          var resError = new Error(
            'Error response. Status '.concat(
              res.statusCode,
              ' from request to: ',
              '${authUrl}',
              '/oauth2/token'
            )
          );
          resError.code = res.statusCode;
          reject(resError);
        }
      });
    });

    req.on('error', function (error) {
      reject(error);
    });

    req.write(postData);
    req.end();
  });
}

function getTokensFromAuth(appData) {
  log.logMessage('token-acquisition', 'Acquiring token from auth');
  return new Promise(function (resolve, reject) {
    authorizeUser(appData.userToken)
      .then(
        function (authCode) {
          getNewCWToken(appData.personaId, authCode, false)
            .then(
              function (tokens) {
                resolve(tokens);
                return;
              },
              function (error) {
                reject(error);
              }
            )
            .catch(function (error) {
              reject(error);
            });
          return;
        },
        function (error) {
          reject(error);
        }
      )
      .catch(function (error) {
        reject(error);
      });
  });
}

function getTokensFromStorage() {
  log.logMessage('token-acquisition', 'Acquiring data from storage');
  return new Promise(function (resolve, reject) {
    filesStore
      .getFile(TOKENS_HASH_FILE_PATH)
      .then(
        function (tokenFile) {
          if (tokenFile.file.fileSize > 0) {
            tokenFile.file.readAsText(
              function (content) {
                try {
                  log.logMessage('token-acquisition', 'Attempting to parse CW data from storage');
                  var storageData = JSON.parse(content);
                  log.logMessage('token-acquisition', 'Successfully received data from storage');
                  resolve({
                    access_token: storageData.access_token,
                    refresh_token: storageData.refresh_token,
                    personaId: storageData.personaId,
                    timestamp: storageData.timestamp,
                  });
                } catch (error) {
                  reject(new Error('Unable to parse file to JSON -> ' + error.message));
                }
              },
              function (error) {
                reject(new Error('Unable to read tokens file as a text -> ' + error.message));
              },
              'UTF-8'
            );
          } else {
            reject(new Error('File is empty'));
          }
          return;
        },
        function () {
          reject(new Error('Error getting tokens file'));
        }
      )
      .catch(function (error) {
        reject(error);
      });
  });
}

function getTokens(appData) {
  return getTokensFromStorage().then(
    function (tokens) {
      var timestampDiff = (Date.now() - tokens.timestamp) / ONE_DAY_IN_MILLISECONDS;

      if (
        appData.personaId &&
        appData.userToken &&
        (timestampDiff > DAYS_USING_STORAGE_DATA || appData.personaId !== tokens.personaId)
      ) {
        return getTokensFromAuth(appData);
      } else {
        return tokens;
      }
    },
    function (error) {
      if (appData.personaId && appData.userToken) {
        log.logMessage(
          'get-tokens',
          'Failed getting tokens from storage, trying to get tokens from Auth',
          error
        );
        return getTokensFromAuth(appData);
      } else {
        return error;
      }
    }
  );
}

function getCWAssets(tokens) {
  log.logMessage('get-assets', 'Attempting to get CW assets');
  return new Promise(function (resolve, reject) {
    if (!tokens.access_token) {
      return reject(new Error('Missing access_token'));
    }
    if (!tokens.refresh_token) {
      return reject(new Error('Missing refresh_token'));
    }

    var options = {
      hostname: '${bffUrl}',
      path: '/bff/partners/samsung/v1/continuewatching',
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'x-skyott-device': 'TV',
        Authorization: 'Bearer ' + tokens.access_token,
        'x-skyint-requestid': log.containerSession,
        'x-skyott-language': 'en',
        'x-skyott-platform': 'SAMSUNG',
        'x-skyott-proposition': 'NBCUOTT',
        'x-skyott-territory': 'US',
        'x-skyott-provider': 'NBCU',
        'x-sky-forwarded-host': '${host}',
      },
    };

    var req = https.request(options, function (res) {
      var data = '';
      res.on('data', function (chunk) {
        data += chunk.toString();
      });

      res.on('end', function () {
        log.logMessage(
          'get-assets',
          'Got CW asset API response (' +
            res.statusCode +
            '), using ' +
            (res.headers ? res.headers.via : '-')
        );
        if (res.statusCode === 200) {
          try {
            log.logMessage('get-assets', 'Attempting to parse data from CW API');
            var response = JSON.parse(data);
            if (
              response.data &&
              response.data.continueWatching &&
              response.data.continueWatching.items
            ) {
              resolve(response.data.continueWatching.items);
            } else {
              reject(new Error('Invalid response from backend -> ' + data));
            }
          } catch (error) {
            reject(new Error('Unable to parse response JSON -> ' + error.message + '-> ' + data));
          }
        } else {
          var resError = new Error(
            'Error response. Status '.concat(res.statusCode, ' from request to: ', '${bffUrl}')
          );
          resError.code = res.statusCode;
          reject(resError);
        }
      });
    });

    req.on('error', function (error) {
      reject(new Error('Cannot get continue watching assets -> ' + error.message));
    });

    req.end();
  });
}

function updateCWRail(items) {
  log.logMessage('update-rail', 'CW rail update started');
  return cwHelper.addCWItems(items).then(
    function () {
      log.logMessage('update-rail', 'CW rail updated successfully');
      // this has to be here, but it seems to be breaking older devices
      // (otherwise the service wont be called every 10 minutes)
      return tizen.application.getCurrentApplication().exit();
    },
    function (error) {
      logErrorAndExit(error);
    }
  );
}

function clearDataAndExit() {
  log.logMessage('revoke-access', 'Clearing user credentials and CW items');

  return Promise.all([filesStore.deleteFile(TOKENS_HASH_FILE_PATH), cwHelper.removeCWItems()]).then(
    //eslint-disable-next-line promise/always-return
    function () {
      tizen.application.getCurrentApplication().exit();
    },
    function () {
      tizen.application.getCurrentApplication().exit();
    }
  );
}

function logErrorAndExit(error) {
  log.logMessage(
    'log-and-exit',
    'Error occurred during the previous step',
    null,
    error && error.message
  );
  tizen.application.getCurrentApplication().exit();
}

module.exports.onStart = function () {};

module.exports.onRequest = function () {
  log.setAction('container-cw');
  log.setShouldLog(Math.random() <= 0.1);
  // get user tokens from shared app data
  var appData = getSharedAppData();
  log.logMessage('service-start', 'Service started, checking appData', appData);

  // app can tell container to revoke cw in case of logout/ff disable
  if (appData.revokeAccess) {
    return clearDataAndExit();
  }

  // all is well, get cw tokens
  getTokens(appData)
    .then(function (tokens) {
      // got the tokens, request cw assets
      return getCWAssets(tokens).then(
        // got cw assets, call service to add said items
        updateCWRail,
        // error getting assets, deal with it
        function handleGetCWAssetsError(error) {
          log.logMessage(
            'get-assets',
            'Error while getting CW assets',
            null,
            error.message + ' ' + error.code
          );

          // docs say that the cw endpoint should fail with a 403 when the token has expired or invalid but it actually returns a 400
          if (error.code >= 400 && error.code < 500) {
            // if we get an error while retrieving assets, we'll try to get a new token once
            getNewCWToken(tokens.personaId, tokens.refresh_token, true)
              .then(
                function (newTokens) {
                  log.logMessage('token-refresh', 'Refreshed token getting assets again');
                  getCWAssets(newTokens).then(
                    updateCWRail, // call the service again
                    logErrorAndExit
                  ).catch;
                  return;
                },
                function (cwTokenError) {
                  log.logMessage(
                    'token-refresh-failed',
                    'Failed to refresh token',
                    appData,
                    cwTokenError.message
                  );

                  return clearDataAndExit();
                }
              )
              .catch(logErrorAndExit);
          } else {
            logErrorAndExit(
              new Error("CW API didn't respond with assets or prompt to refresh tokens")
            );
          }
        }
      );
    })
    .then(function () {
      return;
    }, logErrorAndExit)
    .catch(logErrorAndExit);
};

module.exports.onError = function () {};

module.exports.onExit = function () {};
