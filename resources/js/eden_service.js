var https = require('https');
var crypto = require('crypto');

var SAM_URL = '${sam}';
var SAM_PREVIEW_HASH_FILE_PATH = '${samFileName}';
var CONTAINER_VERSION = '${version}';
var ONE_DAY_IN_MILLISECONDS = 1000 * 60 * 60 * 24;

var currTime = new Date().getTime();
var globalContentFileHash = {};

function getPreviewHashFile() {
  return new Promise(function (resolve) {
    tizen.filesystem.resolve('documents', function (documentsDir) {
      documentsDir.listFiles(function (files) {
        try {
          for (var i = 0; i < files.length; i++) {
            if (files[i].name === SAM_PREVIEW_HASH_FILE_PATH) {
              return resolve({
                dir: documentsDir,
                file: files[i],
              });
            }
          }

          var previewHashFile = documentsDir.createFile(SAM_PREVIEW_HASH_FILE_PATH);

          resolve({
            dir: documentsDir,
            file: previewHashFile,
          });
        } catch (err) {}
      });
    });
  });
}

function getPreviewHash() {
  return new Promise(function (resolve) {
    getPreviewHashFile().then(function (hashFile) {
      hashFile.file.readAsText(
        function (content) {
          if (content && content.length > 0) {
            var parsedContent = content.split('-');
            resolve({
              previewHash: parsedContent[0],
              hashTime: parsedContent[1],
            });
          } else {
            resolve({
              previewHash: undefined,
              hashTime: 0,
            });
          }
        },
        function () {
          resolve({
            previewHash: undefined,
            hashTime: 0,
          });
        },
        'UTF-8'
      );
    });
  });
}

function savePreviewHash(previewHash, currTime) {
  return new Promise(function (resolve) {
    getPreviewHashFile().then(function (hashFile) {
      hashFile.dir.deleteFile(
        hashFile.file.fullPath,
        function () {
          var newFile = hashFile.dir.createFile(SAM_PREVIEW_HASH_FILE_PATH);
          newFile.openStream(
            'w',
            function (fs) {
              fs.write(previewHash + '-' + currTime);
              fs.close();
              resolve();
            },
            function () {
              resolve();
            },
            'UTF-8'
          );
        },
        function () {
          resolve();
        }
      );
    });
  });
}

function fetchPersonalizedPreviewData(userToken, language, activeTerritory) {
  var options = {
    hostname: SAM_URL.slice(0, SAM_URL.indexOf('/')),
    path: SAM_URL.slice(SAM_URL.indexOf('/')),
    method: 'GET',
    headers: {
      'x-skyott-territory': activeTerritory || '${territory}',
      'x-skyott-provider': '${provider}',
      'x-skyott-proposition': '${proposition}',
      'x-skyott-device': 'TV',
      'x-skyott-platform': 'SAMSUNG',
      'x-skyott-language': language || '${defaultLanguage}',
      'x-skyott-client-version': CONTAINER_VERSION,
    },
  };

  if (userToken && userToken.length > 0) {
    options.headers['x-skyott-usertoken'] = userToken;
  }

  var req = https.request(options, function (res) {
    var data = '';
    res.on('data', function (chunk) {
      data += chunk.toString();
    });

    res.on('end', function () {
      setPreview(data);
    });
  });

  req.on('error', function () {
    // Failed to get the personalized preview, try to get fallback directly from ATOM
    fetchFallbackPreviewData();
  });

  req.end();
}

function fetchFallbackPreviewData() {
  var url = '${accelerator}';
  if (url) {
    var req = https.request(url, function (res) {
      var data = '';
      res.on('data', function (chunk) {
        data += chunk.toString();
      });

      res.on('end', function () {
        setPreview(data);
      });
    });

    req.end();
  }
}

function logAppVersionToNewRelic(curTime, hashTime, md5, previewHash) {
  return new Promise(function (resolve) {
    var data = JSON.stringify({
      logtype: 'container-info',
      deviceType: 'Samsung',
      version: CONTAINER_VERSION,
      message: 'setPreviewData triggered by time difference',
      curTime: curTime,
      hashTime: hashTime,
      md5: md5,
      previewHash: previewHash,
    });

    var options = {
      hostname: 'log-api.newrelic.com',
      path: '/log/v1',
      method: 'POST',
      headers: {
        'Api-Key': '${newRelicApiKey}',
        'Content-Type': 'application/json',
        'Content-Length': data.length,
      },
    };

    var req = https.request(options, function () {
      resolve();
    });

    req.on('error', function () {
      resolve();
    });

    req.write(data);
    req.end();
  });
}

function checkIfMoreThanADayPassedThenFetch() {
  getPreviewHash().then(function (contentFileHash) {
    globalContentFileHash = contentFileHash;
    var timeDifference = (currTime - contentFileHash.hashTime) / ONE_DAY_IN_MILLISECONDS;
    if (timeDifference > 1) {
      fetchPersonalizedPreviewData();
    } else {
      tizen.application.getCurrentApplication().exit();
    }
  });
}

function setPreview(previewData) {
  var md5 = crypto.createHash('md5').update(previewData).digest('hex');

  logAppVersionToNewRelic(
    currTime,
    globalContentFileHash.hashTime,
    md5,
    globalContentFileHash.previewHash
  ).then(function () {
    savePreviewHash(md5, currTime).then(function () {
      webapis.preview.setPreviewData(
        previewData,
        function () {
          tizen.application.getCurrentApplication().exit();
        },
        function () {
          tizen.application.getCurrentApplication().exit();
        }
      );
    });
  });
}

function checkForegroundCall() {
  var reqAppControl = tizen.application.getCurrentApplication().getRequestedAppControl();

  if (
    reqAppControl.appControl.operation === 'http://tizen.org/appcontrol/operation/launch_service'
  ) {
    //Get data sent it from foreground app by app control
    var retData = reqAppControl.appControl.data;
    var userToken, language, activeTerritory;

    for (var i = 0; i < retData.length; i++) {
      if (retData[i].key === 'userToken') {
        userToken = retData[i].value[0];
        language = retData[i].value[1];
        activeTerritory = retData[i].value[2];
      }
    }

    fetchPersonalizedPreviewData(userToken, language, activeTerritory);
  } else {
    checkIfMoreThanADayPassedThenFetch();
  }
}

module.exports.onStart = function () {};

module.exports.onRequest = function () {
  checkForegroundCall();
};

module.exports.onExit = function () {};
