# client-container-tizen

A tool for building tizen container projects

## Wiki

Check out [this repository's Wiki](https://gspcloud.atlassian.net/wiki/spaces/PTC/pages/223741268/Samsung+Documentation) for more information.

## Getting started

Install dependencies:

`yarn install`

If you want to build and sideload to a device first install the Tizen SDK, as shown in [this guide](https://gspcloud.atlassian.net/wiki/spaces/PTC/pages/223741268/Samsung+Documentation#%5BhardBreak%5DInstalling-Tizen-Studio). Then you can run the following commands:

1. `yarn build -e [production/stable] -t [US/EU/AU] -p [peacock/showmax/skyshowtime/nowott]` - this copies files for given flags to the `/dist` folder
2. Add a `src/devices.json` file copying the example in `src/devices.example.json`. This file is local to you and should contain the information from your TVs.
3. `yarn package` packages files into a `.wgt` Tizen installable.

You can then install or launch the app in debug mode as follows:

1. `yarn sideload [DEVICE_ID || DEVICE_ALIAS]` installs `.wgt` onto given devices, you may need to add your device to `src/devices.json`. In case you want to uninstall the previously installed package you can add `-u` or `--uninstall` to the command.

2. Or `yarn debug [DEVICE_ID || DEVICE_ALIAS]` starts the app (without installing it) in debug mode. Outputs deviceId:debugPort to use with Chrome, as shown in the output below.

```
yarn debug office-2019
yarn run v1.22.19
$ node src/debug-js office-2019
result ... successfully launched pid = 4190 with debug 1 port: 45729
DEBUG URL: http://10.18.45.120:45729
```

## Build Flags

You can see the build flag options by running

`yarn build -h`

## Available Flags

- proposition `-p` or `--proposition` - `nowott`, `showmax` `skyshowtime` or `peacock`
- territory `-t` or `--territory` - `GB`, `IE`, `IT`, `DE`, `AU`, `EU` or `US`
- environment `-e` or `--environment` - `production`, `preview`, `rc`, `stable`, `stable-preview`, `proton`, `development`
- featureflags `-f` or `--featureflags` - string separated by commas `"flag1, flag2"`
- customURL `-c`or `--customUrl` - string to override the build with a full URL
- deviceConfig `-d` or `--device` - defines which device config to build with: `default`, `oobe-voice`

## Examples

### Peacock production

```
yarn build -e production -t US -p peacock
```

### Skyshowtime production

```
yarn build -e production -t EU -p skyshowtime
```

### Showmax production

```
yarn build -e production -t AU -p showmax
```

### NowOTT production

```
yarn build -e production -t [GB/DE/IT] -p nowott
```

### Using feature flags

```
yarn build -f "enableDrmLicense, abTesting, brightline"
```

### Using custom URL

```
yarn build -c "https://peacocktv.com"
```

### Build w/ Out Of The Box (oobe) experience support and voice interaction support

```
yarn build -e production -t [US/EU/AU] -p [peacock/showmax/skyshowtime/nowott] -d oobe-voice
```

## Output

The output of any of these commands presented above is a `dist` folder, which can be either turned into a `.wgt` file, which can be launched as described in [Getting started](#getting-started) , or imported into TizenStudio, as such:

`File > Import > Tizen > Tizen Project > Root Directory (choose the dist folder) > Choose 'tv-samsung' in the Profile dropdown list`

After you have succesfully imported the `dist` folder into Tizen Studio, to install the application on your TV, right click on the folder on the left of the screen and then either:

1. Install the application normally - `Run as > Tizen Web Application`

2. Or install the application in debug mode - `Debug as > Tizen Web Application`. This will result in a Chrome window poping up that has the url to access the devtools of the device. From that url, we only need [DEVICE_IP]:[PORT].

## Device Version

| Year | Platform  | Chromium Version |
| ---- | --------- | ---------------- |
| 2024 | Tizen 8.0 | 108.0.5359.1     |
| 2023 | Tizen 7.0 | 94.0.4606.31     |
| 2022 | Tizen 6.5 | 85.0.4183.93     |
| 2021 | Tizen 6.0 | 76.0.3809.146    |
| 2020 | Tizen 5.5 | 69.0.3497.106    |
| 2019 | Tizen 5.0 | -                |
| 2018 | Tizen 4.0 | -                |
| 2017 | Tizen 3.0 | -                |
| 2016 | Tizen 2.4 | -                |

For concrete information on TV Model Groups, check [Samsung's documentation on this topic](https://developer.samsung.com/smarttv/develop/specifications/tv-model-groups.html).
